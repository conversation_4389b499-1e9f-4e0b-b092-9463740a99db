import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:contentpal/generated/l10n/app_localizations.dart';
import 'package:contentpal/services/localization_service.dart';

void main() {
  group('HomePage Internationalization Tests', () {
    testWidgets('HomePage should display localized strings in English', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          locale: const Locale('en'),
          localizationsDelegates: const [
            AppLocalizations.delegate,
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          supportedLocales: LocalizationService.supportedLocales,
          home: Builder(
            builder: (context) {
              final l10n = AppLocalizations.of(context);
              return Scaffold(
                body: Column(
                  children: [
                    Text(l10n.appName),
                    Text(l10n.appNameChinese),
                    Text(l10n.myContentLibrary),
                    Text(l10n.manageAndBrowseContent),
                    Text(l10n.recommendedTools),
                    Text(l10n.markdownTitle),
                    Text(l10n.markdownDescription),
                    Text(l10n.textCardsTitle),
                    Text(l10n.textCardsDescription),
                    Text(l10n.trafficGuideTitle),
                    Text(l10n.trafficGuideDescription),
                    Text(l10n.fileTools),
                    Text(l10n.svgTitle),
                    Text(l10n.svgDescription),
                    Text(l10n.htmlTitle),
                    Text(l10n.htmlDescription),
                    Text(l10n.loadingContent),
                  ],
                ),
              );
            },
          ),
        ),
      );

      // Verify English translations
      expect(find.text('ContentPal'), findsOneWidget);
      expect(find.text('内容君'), findsOneWidget);
      expect(find.text('My Content Library'), findsOneWidget);
      expect(find.text('Manage and browse all your content'), findsOneWidget);
      expect(find.text('Recommended Tools'), findsOneWidget);
      expect(find.text('Markdown'), findsOneWidget);
      expect(find.text('Document editing and rendering'), findsOneWidget);
      expect(find.text('Text Cards'), findsOneWidget);
      expect(find.text('Knowledge card customization and rendering'), findsOneWidget);
      expect(find.text('Traffic Guide'), findsOneWidget);
      expect(find.text('Traffic image and text processing'), findsOneWidget);
      expect(find.text('File Tools'), findsOneWidget);
      expect(find.text('SVG'), findsOneWidget);
      expect(find.text('Vector graphics processing'), findsOneWidget);
      expect(find.text('HTML'), findsOneWidget);
      expect(find.text('Web content editing'), findsOneWidget);
      expect(find.text('Loading content...'), findsOneWidget);
    });

    testWidgets('HomePage should display localized strings in Chinese', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          locale: const Locale('zh'),
          localizationsDelegates: const [
            AppLocalizations.delegate,
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          supportedLocales: LocalizationService.supportedLocales,
          home: Builder(
            builder: (context) {
              final l10n = AppLocalizations.of(context);
              return Scaffold(
                body: Column(
                  children: [
                    Text(l10n.appName),
                    Text(l10n.appNameChinese),
                    Text(l10n.myContentLibrary),
                    Text(l10n.manageAndBrowseContent),
                    Text(l10n.recommendedTools),
                    Text(l10n.markdownTitle),
                    Text(l10n.markdownDescription),
                    Text(l10n.textCardsTitle),
                    Text(l10n.textCardsDescription),
                    Text(l10n.trafficGuideTitle),
                    Text(l10n.trafficGuideDescription),
                    Text(l10n.fileTools),
                    Text(l10n.svgTitle),
                    Text(l10n.svgDescription),
                    Text(l10n.htmlTitle),
                    Text(l10n.htmlDescription),
                    Text(l10n.loadingContent),
                  ],
                ),
              );
            },
          ),
        ),
      );

      // Verify Chinese translations
      expect(find.text('ContentPal'), findsOneWidget);
      expect(find.text('内容君'), findsOneWidget);
      expect(find.text('我的内容库'), findsOneWidget);
      expect(find.text('管理和浏览您的所有内容'), findsOneWidget);
      expect(find.text('推荐工具'), findsOneWidget);
      expect(find.text('Markdown'), findsOneWidget);
      expect(find.text('文档编辑与渲染'), findsOneWidget);
      expect(find.text('文本卡片'), findsOneWidget);
      expect(find.text('知识卡片定制渲染'), findsOneWidget);
      expect(find.text('内容引流'), findsOneWidget);
      expect(find.text('引流图片与文本处理'), findsOneWidget);
      expect(find.text('文件工具'), findsOneWidget);
      expect(find.text('SVG'), findsOneWidget);
      expect(find.text('矢量图形处理'), findsOneWidget);
      expect(find.text('HTML'), findsOneWidget);
      expect(find.text('网页内容编辑'), findsOneWidget);
      expect(find.text('正在加载内容...'), findsOneWidget);
    });

    testWidgets('HomePage should display localized strings in Japanese', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          locale: const Locale('ja'),
          localizationsDelegates: const [
            AppLocalizations.delegate,
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          supportedLocales: LocalizationService.supportedLocales,
          home: Builder(
            builder: (context) {
              final l10n = AppLocalizations.of(context);
              return Scaffold(
                body: Column(
                  children: [
                    Text(l10n.appName),
                    Text(l10n.appNameChinese),
                    Text(l10n.myContentLibrary),
                    Text(l10n.manageAndBrowseContent),
                    Text(l10n.recommendedTools),
                    Text(l10n.markdownTitle),
                    Text(l10n.markdownDescription),
                    Text(l10n.textCardsTitle),
                    Text(l10n.textCardsDescription),
                    Text(l10n.trafficGuideTitle),
                    Text(l10n.trafficGuideDescription),
                    Text(l10n.fileTools),
                    Text(l10n.svgTitle),
                    Text(l10n.svgDescription),
                    Text(l10n.htmlTitle),
                    Text(l10n.htmlDescription),
                    Text(l10n.loadingContent),
                  ],
                ),
              );
            },
          ),
        ),
      );

      // Verify Japanese translations
      expect(find.text('ContentPal'), findsOneWidget);
      expect(find.text('内容君'), findsOneWidget);
      expect(find.text('マイコンテンツライブラリ'), findsOneWidget);
      expect(find.text('すべてのコンテンツを管理・閲覧'), findsOneWidget);
      expect(find.text('おすすめツール'), findsOneWidget);
      expect(find.text('Markdown'), findsOneWidget);
      expect(find.text('ドキュメント編集とレンダリング'), findsOneWidget);
      expect(find.text('テキストカード'), findsOneWidget);
      expect(find.text('ナレッジカードのカスタマイズとレンダリング'), findsOneWidget);
      expect(find.text('トラフィックガイド'), findsOneWidget);
      expect(find.text('トラフィック画像とテキスト処理'), findsOneWidget);
      expect(find.text('ファイルツール'), findsOneWidget);
      expect(find.text('SVG'), findsOneWidget);
      expect(find.text('ベクターグラフィック処理'), findsOneWidget);
      expect(find.text('HTML'), findsOneWidget);
      expect(find.text('ウェブコンテンツ編集'), findsOneWidget);
      expect(find.text('コンテンツを読み込み中...'), findsOneWidget);
    });
  });
}
