import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:contentpal/generated/l10n/app_localizations.dart';
import 'package:contentpal/services/localization_service.dart';

void main() {
  group('Internationalization Tests', () {
    testWidgets('AppLocalizations should load correctly for English', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          locale: const Locale('en'),
          localizationsDelegates: const [
            AppLocalizations.delegate,
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          supportedLocales: LocalizationService.supportedLocales,
          home: Builder(
            builder: (context) {
              final l10n = AppLocalizations.of(context);
              return Scaffold(
                body: Column(
                  children: [
                    Text(l10n.appName),
                    Text(l10n.home),
                    Text(l10n.settings),
                    Text(l10n.language),
                    Text(l10n.theme),
                    Text(l10n.contentLibrary),
                    Text(l10n.templateLibrary),
                  ],
                ),
              );
            },
          ),
        ),
      );

      // Verify English translations
      expect(find.text('ContentPal'), findsOneWidget);
      expect(find.text('Home'), findsOneWidget);
      expect(find.text('Settings'), findsOneWidget);
      expect(find.text('Language'), findsOneWidget);
      expect(find.text('Theme'), findsOneWidget);
      expect(find.text('Content Library'), findsOneWidget);
      expect(find.text('Template Library'), findsOneWidget);
    });

    testWidgets('AppLocalizations should load correctly for Chinese', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          locale: const Locale('zh'),
          localizationsDelegates: const [
            AppLocalizations.delegate,
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          supportedLocales: LocalizationService.supportedLocales,
          home: Builder(
            builder: (context) {
              final l10n = AppLocalizations.of(context);
              return Scaffold(
                body: Column(
                  children: [
                    Text(l10n.appName),
                    Text(l10n.home),
                    Text(l10n.settings),
                    Text(l10n.language),
                    Text(l10n.theme),
                    Text(l10n.contentLibrary),
                    Text(l10n.templateLibrary),
                  ],
                ),
              );
            },
          ),
        ),
      );

      // Verify Chinese translations
      expect(find.text('ContentPal'), findsOneWidget);
      expect(find.text('首页'), findsOneWidget);
      expect(find.text('设置'), findsOneWidget);
      expect(find.text('语言'), findsOneWidget);
      expect(find.text('主题'), findsOneWidget);
      expect(find.text('内容库'), findsOneWidget);
      expect(find.text('模板库'), findsOneWidget);
    });

    testWidgets('AppLocalizations should load correctly for Japanese', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          locale: const Locale('ja'),
          localizationsDelegates: const [
            AppLocalizations.delegate,
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          supportedLocales: LocalizationService.supportedLocales,
          home: Builder(
            builder: (context) {
              final l10n = AppLocalizations.of(context);
              return Scaffold(
                body: Column(
                  children: [
                    Text(l10n.appName),
                    Text(l10n.home),
                    Text(l10n.settings),
                    Text(l10n.language),
                    Text(l10n.theme),
                    Text(l10n.contentLibrary),
                    Text(l10n.templateLibrary),
                  ],
                ),
              );
            },
          ),
        ),
      );

      // Verify Japanese translations
      expect(find.text('ContentPal'), findsOneWidget);
      expect(find.text('ホーム'), findsOneWidget);
      expect(find.text('設定'), findsOneWidget);
      expect(find.text('言語'), findsOneWidget);
      expect(find.text('テーマ'), findsOneWidget);
      expect(find.text('コンテンツライブラリ'), findsOneWidget);
      expect(find.text('テンプレートライブラリ'), findsOneWidget);
    });

    test('LocalizationService should support all required locales', () {
      const supportedLocales = LocalizationService.supportedLocales;
      
      expect(supportedLocales.length, 3);
      expect(supportedLocales.any((locale) => locale.languageCode == 'en'), true);
      expect(supportedLocales.any((locale) => locale.languageCode == 'zh'), true);
      expect(supportedLocales.any((locale) => locale.languageCode == 'ja'), true);
    });

    test('LocalizationService should provide correct locale names', () {
      const localeNames = LocalizationService.localeNames;
      
      expect(localeNames['en'], 'English');
      expect(localeNames['zh'], '中文');
      expect(localeNames['ja'], '日本語');
    });
  });
}
