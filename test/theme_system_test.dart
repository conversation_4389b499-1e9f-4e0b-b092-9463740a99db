import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:contentpal/config/app_theme.dart';
import 'package:contentpal/config/app_settings.dart';
import 'package:contentpal/services/settings_service.dart';

void main() {
  group('Theme System Tests', () {
    test('AppTheme should provide correct themes for different types', () {
      // Test light theme
      final lightTheme = AppTheme.getTheme(AppThemeType.materialYou, false);
      expect(lightTheme.brightness, Brightness.light);
      expect(lightTheme.colorScheme.brightness, Brightness.light);

      // Test dark theme
      final darkTheme = AppTheme.getTheme(AppThemeType.materialYou, true);
      expect(darkTheme.brightness, Brightness.dark);
      expect(darkTheme.colorScheme.brightness, Brightness.dark);
    });

    test('AppSettings should have correct default theme mode', () {
      final settings = AppSettings();
      expect(settings.themeMode, ThemeMode.system);
      expect(settings.themeType, AppThemeType.materialYou);
    });

    test('AppSettings should serialize and deserialize correctly', () {
      final originalSettings = AppSettings(
        themeMode: ThemeMode.dark,
        themeType: AppThemeType.chineseTheme,
      );

      final json = originalSettings.toJson();
      final deserializedSettings = AppSettings.fromJson(json);

      expect(deserializedSettings.themeMode, ThemeMode.dark);
      expect(deserializedSettings.themeType, AppThemeType.chineseTheme);
    });

    test('All theme types should have both light and dark variants', () {
      for (final themeType in AppThemeType.values) {
        final lightTheme = AppTheme.getTheme(themeType, false);
        final darkTheme = AppTheme.getTheme(themeType, true);

        expect(lightTheme, isNotNull);
        expect(darkTheme, isNotNull);
        expect(lightTheme.colorScheme.brightness, Brightness.light);
        expect(darkTheme.colorScheme.brightness, Brightness.dark);
      }
    });

    test('Theme colors should be accessible', () {
      for (final themeType in AppThemeType.values) {
        final themeColor = AppTheme.getThemeColor(themeType);
        expect(themeColor, isNotNull);
        expect(themeColor, isA<Color>());
      }
    });

    test('AppTheme.adaptDarkColor should work correctly', () {
      const lightColor = Colors.white;
      const darkColor = Colors.black;

      final lightResult = AppTheme.adaptDarkColor(lightColor, darkColor, false);
      final darkResult = AppTheme.adaptDarkColor(lightColor, darkColor, true);

      expect(lightResult, lightColor);
      expect(darkResult, darkColor);
    });
  });
}
