import 'package:flutter/material.dart';
import '../../config/app_theme.dart';
import '../../content/content_home_page.dart';
import '../../html/html_manager_screen.dart';
import '../../markdown/markdown_render_screen.dart';
import '../../pdf/pdf_manager_screen.dart';
import '../../svg/svg_manager_screen.dart';
import '../../voice/voice_home_page.dart';
import 'create_content_option.dart';
import '../../generated/l10n/app_localizations.dart';

/// 创建内容底部菜单组件
class CreateContentBottomSheet extends StatelessWidget {
  const CreateContentBottomSheet({super.key});

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 30, horizontal: 24),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            l10n.createNewContent,
            style: const TextStyle(
              fontSize: 22,
              fontWeight: FontWeight.bold,
              color: AppTheme.textDarkColor,
            ),
          ),
          const SizedBox(height: 10),
          Text(
            l10n.selectContentType,
            style: const TextStyle(
              fontSize: 14,
              color: AppTheme.textMediumColor,
            ),
          ),
          const SizedBox(height: 30),
          SizedBox(
            height: 100,
            child: ListView(
              scrollDirection: Axis.horizontal,
              children: [
                CreateContentOption(
                  icon: Icons.dashboard_rounded,
                  label: '内容库',
                  gradient: AppTheme.chineseGradient,
                  onTap: () {
                    Navigator.pop(context);
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const ContentHomePage(),
                      ),
                    );
                  },
                ),
                CreateContentOption(
                  icon: Icons.text_fields,
                  label: 'Markdown',
                  gradient: AppTheme.blueGradient,
                  onTap: () {
                    Navigator.pop(context);
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const MarkdownRenderScreen(),
                      ),
                    );
                  },
                ),
                CreateContentOption(
                  icon: Icons.image,
                  label: 'SVG',
                  gradient: AppTheme.purpleGradient,
                  onTap: () {
                    Navigator.pop(context);
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const SvgManagerScreen(),
                      ),
                    );
                  },
                ),
                CreateContentOption(
                  icon: Icons.code,
                  label: 'HTML',
                  gradient: AppTheme.greenGradient,
                  onTap: () {
                    Navigator.pop(context);
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const HtmlManagerScreen(),
                      ),
                    );
                  },
                ),
                CreateContentOption(
                  icon: Icons.picture_as_pdf,
                  label: 'PDF',
                  gradient: AppTheme.orangeGradient,
                  onTap: () {
                    Navigator.pop(context);
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const PdfManagerScreen(),
                      ),
                    );
                  },
                ),
                CreateContentOption(
                  icon: Icons.mic,
                  label: '语音',
                  gradient: AppTheme.purpleGradient,
                  onTap: () {
                    Navigator.pop(context);
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const VoiceHomePage(),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
