import 'package:flutter/material.dart';
import '../config/app_theme.dart';
import '../models/content_item.dart';
import '../models/content_render_data.dart';
import '../services/content_service.dart';
import 'widgets/modern_content_card.dart';
import 'content_detail_page.dart';
import '../generated/l10n/app_localizations.dart';

/// 内容库演示页面
/// 展示新的内容库功能，包括渲染结果显示、现代化UI等
class ContentLibraryDemoPage extends StatefulWidget {
  const ContentLibraryDemoPage({super.key});

  @override
  State<ContentLibraryDemoPage> createState() => _ContentLibraryDemoPageState();
}

class _ContentLibraryDemoPageState extends State<ContentLibraryDemoPage> {
  final ContentService _contentService = ContentService();
  List<ContentItem> _demoItems = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _initializeDemoData();
  }

  /// 初始化演示数据
  Future<void> _initializeDemoData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      await _contentService.initialize();
      _createDemoItems();
    } catch (e) {
      debugPrint('初始化演示数据失败: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// 创建演示内容项
  void _createDemoItems() {
    _demoItems = [
      // Markdown文档示例
      ContentItem(
        title: 'Markdown文档示例',
        type: ContentType.markdown,
        content: '''# 欢迎使用ContentPal

这是一个**Markdown文档**示例，展示了新的内容库功能。

## 主要特性

- 🎨 现代化UI设计
- 🖼️ 渲染结果预览
- 📱 响应式布局
- ⚡ 高性能渲染

### 代码示例

```dart
void main() {
  runApp(MyApp());
}
```

> 这是一个引用块，用来展示重要信息。

**ContentPal** 让内容管理变得更加简单高效！''',
        tags: ['演示', 'Markdown', '文档'],
        renderData: ContentRenderData(
          renderType: RenderType.markdown,
          primaryImagePath: '/demo/markdown_render.png',
          thumbnailPath: '/demo/markdown_thumb.png',
          originalContent: 'markdown content',
          renderConfig: RenderConfig.defaultMarkdown,
        ),
      ),

      // Markdown分块示例
      ContentItem(
        title: 'Markdown分块文档',
        type: ContentType.markdownBlocks,
        content: '''# 第一部分：介绍

这是分块模式的第一部分内容。

# 第二部分：功能特性

分块模式可以将长文档分割为多个独立的渲染块。

# 第三部分：使用方法

每个块都可以独立渲染和显示。''',
        tags: ['演示', 'Markdown', '分块'],
        renderData: ContentRenderData(
          renderType: RenderType.markdownBlocks,
          imagePaths: [
            '/demo/block1.png',
            '/demo/block2.png',
            '/demo/block3.png',
          ],
          thumbnailPath: '/demo/blocks_thumb.png',
          originalContent: 'markdown blocks content',
          renderConfig: RenderConfig.defaultBlocks,
        ),
      ),

      // 文本卡片示例
      ContentItem(
        title: '精美文本卡片',
        type: ContentType.textCard,
        content: '{"title": "每日一句", "content": "生活不是等待暴风雨过去，而是学会在雨中跳舞。", "author": "佚名", "template": "modern"}',
        tags: ['演示', '文本卡片', '设计'],
        renderData: ContentRenderData(
          renderType: RenderType.textCard,
          primaryImagePath: '/demo/text_card.png',
          thumbnailPath: '/demo/text_card_thumb.png',
          originalContent: 'text card data',
          renderConfig: RenderConfig.defaultTextCard,
        ),
      ),

      // 文本卡片合集示例
      ContentItem(
        title: '励志语录合集',
        type: ContentType.textCardCollection,
        content: '{"cards": [{"title": "坚持", "content": "成功的秘诀在于坚持不懈"}, {"title": "梦想", "content": "梦想是心灵的翅膀"}, {"title": "努力", "content": "努力不一定成功，但不努力一定失败"}]}',
        tags: ['演示', '卡片合集', '励志'],
        renderData: ContentRenderData(
          renderType: RenderType.textCardCollection,
          imagePaths: [
            '/demo/card1.png',
            '/demo/card2.png',
            '/demo/card3.png',
          ],
          thumbnailPath: '/demo/collection_thumb.png',
          originalContent: 'card collection data',
          renderConfig: RenderConfig.defaultTextCard,
        ),
      ),

      // PDF文档示例
      ContentItem(
        title: '技术文档.pdf',
        type: ContentType.pdf,
        content: '/demo/tech_doc.pdf',
        tags: ['演示', 'PDF', '技术'],
        fileSize: 2048576, // 2MB
        renderData: ContentRenderData(
          renderType: RenderType.pdfPreview,
          imagePaths: [
            '/demo/pdf_page1.png',
            '/demo/pdf_page2.png',
            '/demo/pdf_page3.png',
          ],
          thumbnailPath: '/demo/pdf_thumb.png',
          originalContent: '/demo/tech_doc.pdf',
        ),
      ),

      // 语音文件示例
      ContentItem(
        title: '会议录音',
        type: ContentType.voice,
        content: '/demo/meeting_audio.mp3',
        tags: ['演示', '语音', '会议'],
        fileSize: 5242880, // 5MB
        renderData: ContentRenderData(
          renderType: RenderType.voiceWaveform,
          primaryImagePath: '/demo/waveform.png',
          thumbnailPath: '/demo/waveform_thumb.png',
          originalContent: '/demo/meeting_audio.mp3',
        ),
      ),

      // SVG图形示例
      ContentItem(
        title: '图标设计',
        type: ContentType.svg,
        content: '<svg>...</svg>',
        tags: ['演示', 'SVG', '图标'],
        fileSize: 4096, // 4KB
      ),

      // HTML文档示例
      ContentItem(
        title: '网页模板',
        type: ContentType.html,
        content: '<html><head><title>Demo</title></head><body><h1>Hello World</h1></body></html>',
        tags: ['演示', 'HTML', '网页'],
        fileSize: 1024, // 1KB
      ),
    ];

    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    return Scaffold(
      backgroundColor: AppTheme.bgLightColor,
      appBar: AppBar(
        title: const Text(
          '内容库演示',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: AppTheme.textDarkColor,
          ),
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios, color: AppTheme.textDarkColor),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: _isLoading
              ? Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                    const CircularProgressIndicator(),
                    const SizedBox(height: 16),
                    Text(l10n.loadingDemoData),
                ],
              ),
            )
          : Column(
              children: [
                // 功能介绍卡片
                _buildFeatureCard(),
                
                // 内容列表
                Expanded(
                  child: ListView.builder(
                    padding: const EdgeInsets.all(16),
                    itemCount: _demoItems.length,
                    itemBuilder: (context, index) {
                      final item = _demoItems[index];
                      return ModernContentCard(
                        item: item,
                        onTap: () => _viewItem(item),
                        onEdit: () => _editItem(item),
                        onDelete: () => _deleteItem(item),
                        onShare: () => _shareItem(item),
                      );
                    },
                  ),
                ),
              ],
            ),
    );
  }

  /// 构建功能介绍卡片
  Widget _buildFeatureCard() {
    final l10n = AppLocalizations.of(context);
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: AppTheme.primaryGradient,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppTheme.primaryColor.withValues(alpha: 0.3),
            offset: const Offset(0, 4),
            blurRadius: 12,
            spreadRadius: 0,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(
                  Icons.auto_awesome,
                  color: Colors.white,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              const Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '全新内容库体验',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    SizedBox(height: 4),
                    Text(
                      '支持多种内容类型，优先显示渲染结果',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.white,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Text(
            l10n.modernUIDesign,
            style: const TextStyle(
              fontSize: 14,
              color: Colors.white,
              height: 1.5,
            ),
          ),
        ],
      ),
    );
  }

  /// 查看内容项
  void _viewItem(ContentItem item) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ContentDetailPage(
          item: item,
          onItemUpdated: () {
            // 刷新演示数据
            setState(() {});
          },
        ),
      ),
    );
  }

  /// 编辑内容项
  void _editItem(ContentItem item) {
    final l10n = AppLocalizations.of(context);
    _showSnackBar(l10n.editFunction(item.title));
  }

  /// 删除内容项
  void _deleteItem(ContentItem item) {
    final l10n = AppLocalizations.of(context);
    setState(() {
      _demoItems.removeWhere((i) => i.id == item.id);
    });
    _showSnackBar(l10n.deleted(item.title));
  }

  /// 分享内容项
  void _shareItem(ContentItem item) {
    final l10n = AppLocalizations.of(context);
    _showSnackBar(l10n.shareFunction(item.title));
  }

  /// 显示提示信息
  void _showSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
      ),
    );
  }
}
