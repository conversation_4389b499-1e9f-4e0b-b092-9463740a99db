import 'package:flutter/material.dart';
import '../config/app_theme.dart';
import '../generated/l10n/app_localizations.dart';
import '../services/localization_service.dart';

/// 语言设置页面
class LanguageSettingsPage extends StatefulWidget {
  final LocalizationService localizationService;

  const LanguageSettingsPage({super.key, required this.localizationService});

  @override
  State<LanguageSettingsPage> createState() => _LanguageSettingsPageState();
}

class _LanguageSettingsPageState extends State<LanguageSettingsPage> {
  late LocaleOption _selectedOption;

  @override
  void initState() {
    super.initState();
    _selectedOption = _getCurrentOption();
  }

  LocaleOption _getCurrentOption() {
    final options = widget.localizationService.getLocaleOptions();
    final currentLocale = widget.localizationService.currentLocale;

    return options.firstWhere(
      (option) => option.locale == currentLocale,
      orElse: () => options.first, // 默认选择"跟随系统"
    );
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    final options = widget.localizationService.getLocaleOptions();

    return Scaffold(
      backgroundColor: AppTheme.bgLightColor,
      appBar: AppBar(
        title: Text(
          l10n.language,
          style: const TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: AppTheme.textDarkColor,
          ),
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios, color: AppTheme.textDarkColor),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: Column(
        children: [
          // 页面标题和描述
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(24),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  l10n.language,
                  style: const TextStyle(
                    fontSize: 28,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.textDarkColor,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  '选择您偏好的应用语言',
                  style: TextStyle(
                    fontSize: 16,
                    color: AppTheme.textMediumColor,
                  ),
                ),
              ],
            ),
          ),

          // 语言选项列表
          Expanded(
            child: Container(
              margin: const EdgeInsets.symmetric(horizontal: 16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.05),
                    offset: const Offset(0, 2),
                    blurRadius: 10,
                    spreadRadius: 0,
                  ),
                ],
              ),
              child: ListView.separated(
                padding: const EdgeInsets.all(8),
                itemCount: options.length,
                separatorBuilder:
                    (context, index) => Divider(
                      height: 1,
                      color: AppTheme.borderColor,
                      indent: 16,
                      endIndent: 16,
                    ),
                itemBuilder: (context, index) {
                  final option = options[index];
                  final isSelected = option == _selectedOption;

                  return ListTile(
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 20,
                      vertical: 8,
                    ),
                    leading: Container(
                      width: 40,
                      height: 40,
                      decoration: BoxDecoration(
                        color:
                            isSelected
                                ? AppTheme.primaryColor.withValues(alpha: 0.1)
                                : AppTheme.borderColor.withValues(alpha: 0.3),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Icon(
                        _getLanguageIcon(option.code),
                        color:
                            isSelected
                                ? AppTheme.primaryColor
                                : AppTheme.textMediumColor,
                        size: 20,
                      ),
                    ),
                    title: Text(
                      option.displayName,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight:
                            isSelected ? FontWeight.w600 : FontWeight.normal,
                        color:
                            isSelected
                                ? AppTheme.textDarkColor
                                : AppTheme.textMediumColor,
                      ),
                    ),
                    subtitle:
                        option.code != 'system'
                            ? Text(
                              option.name,
                              style: TextStyle(
                                fontSize: 14,
                                color: AppTheme.textLightColor,
                              ),
                            )
                            : null,
                    trailing:
                        isSelected
                            ? Icon(
                              Icons.check_circle,
                              color: AppTheme.primaryColor,
                              size: 24,
                            )
                            : null,
                    onTap: () => _selectLanguage(option),
                  );
                },
              ),
            ),
          ),

          // 底部说明
          Container(
            padding: const EdgeInsets.all(24),
            child: Text(
              l10n.languageChangeEffect,
              style: TextStyle(fontSize: 14, color: AppTheme.textLightColor),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }

  /// 获取语言对应的图标
  IconData _getLanguageIcon(String code) {
    switch (code) {
      case 'system':
        return Icons.settings;
      case 'en':
        return Icons.language;
      case 'zh':
        return Icons.translate;
      case 'ja':
        return Icons.translate;
      default:
        return Icons.language;
    }
  }

  /// 选择语言
  Future<void> _selectLanguage(LocaleOption option) async {
    if (option == _selectedOption) return;

    setState(() {
      _selectedOption = option;
    });

    // 应用语言设置
    await widget.localizationService.setLocale(option.locale);

    // 显示成功提示
    if (mounted) {
      final l10n = AppLocalizations.of(context);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(l10n.languageChangedTo(option.displayName)),
          duration: const Duration(seconds: 2),
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
        ),
      );
    }
  }
}
