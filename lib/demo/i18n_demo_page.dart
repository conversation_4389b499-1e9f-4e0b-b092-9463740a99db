import 'package:flutter/material.dart';
import '../config/app_theme.dart';
import '../generated/l10n/app_localizations.dart';
import '../services/localization_service.dart';

/// 国际化演示页面
class I18nDemoPage extends StatelessWidget {
  final LocalizationService localizationService;

  const I18nDemoPage({super.key, required this.localizationService});

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    
    return Scaffold(
      appBar: AppBar(
        title: Text(l10n.i18nDemo),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 当前语言信息
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '${l10n.language}: ${localizationService.currentLocaleName}',
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Locale: ${localizationService.currentLocale?.toString() ?? "System"}',
                      style: const TextStyle(
                        fontSize: 14,
                        color: Colors.grey,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 24),
            
            // 示例文本
            Text(
              '${l10n.i18nDemo} - ${l10n.viewMultiLanguageSupport}',
              style: const TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            
            const SizedBox(height: 16),
            
            // 各种UI元素的翻译示例
            Expanded(
              child: ListView(
                children: [
                  _buildDemoSection(l10n.home, l10n.appName),
                  _buildDemoSection(l10n.settings, l10n.appearance),
                  _buildDemoSection(l10n.contentLibrary, l10n.manageAllCards),
                  _buildDemoSection(l10n.templateLibrary, l10n.browseBeautifulTemplates),
                  _buildDemoSection(l10n.markdownTitle, l10n.markdownDescription),
                  _buildDemoSection(l10n.textCardsTitle, l10n.textCardsDescription),
                  _buildDemoSection(l10n.trafficGuideTitle, l10n.trafficGuideDescription),
                  _buildDemoSection(l10n.svgTitle, l10n.svgDescription),
                  _buildDemoSection(l10n.htmlTitle, l10n.htmlDescription),
                ],
              ),
            ),
            
            const SizedBox(height: 16),
            
            // 语言切换按钮
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                ElevatedButton(
                  onPressed: () => localizationService.setLocale(const Locale('en')),
                  child: const Text('English'),
                ),
                ElevatedButton(
                  onPressed: () => localizationService.setLocale(const Locale('zh')),
                  child: const Text('中文'),
                ),
                ElevatedButton(
                  onPressed: () => localizationService.setLocale(const Locale('ja')),
                  child: const Text('日本語'),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // 跟随系统按钮
            Center(
              child: ElevatedButton(
                onPressed: () => localizationService.setLocale(null),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.primaryColor,
                  foregroundColor: Colors.white,
                ),
                child: Text(l10n.followSystem),
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildDemoSection(String title, String subtitle) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        title: Text(
          title,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Text(subtitle),
        leading: const Icon(Icons.translate, color: AppTheme.primaryColor),
      ),
    );
  }
}
