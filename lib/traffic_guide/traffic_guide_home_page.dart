import 'package:flutter/material.dart';
import 'package:uuid/uuid.dart';
import '../config/app_theme.dart';
import 'models/traffic_guide_models.dart';
import 'services/traffic_guide_service.dart';

import 'screens/advanced_image_generator_screen.dart';
import 'screens/text_transformer_screen.dart';
import 'screens/watermark_screen.dart';
import 'screens/project_editor_screen.dart';

class TrafficGuideHomePage extends StatefulWidget {
  const TrafficGuideHomePage({super.key});

  @override
  State<TrafficGuideHomePage> createState() => _TrafficGuideHomePageState();
}

class _TrafficGuideHomePageState extends State<TrafficGuideHomePage> {
  final TrafficGuideService _service = TrafficGuideService();
  List<TrafficGuideProject> _projects = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadProjects();
  }

  Future<void> _loadProjects() async {
    setState(() => _isLoading = true);
    final projects = await _service.getAllProjects();
    setState(() {
      _projects = projects;
      _isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Scaffold(
      backgroundColor:
          isDarkMode ? AppTheme.darkBgColor : AppTheme.bgLightColor,
      appBar: AppBar(
        title: const Text(
          '内容引流工具',
          style: TextStyle(fontWeight: FontWeight.w600, fontSize: 20),
        ),
        backgroundColor:
            isDarkMode ? AppTheme.darkBgLightColor : AppTheme.bgWhiteColor,
        foregroundColor:
            isDarkMode ? AppTheme.darkTextColor : AppTheme.textDarkColor,
        elevation: 0,
        scrolledUnderElevation: 1,
        shadowColor:
            isDarkMode ? AppTheme.darkBorderColor : AppTheme.borderColor,
        actions: [
          Container(
            margin: const EdgeInsets.only(right: 8),
            decoration: BoxDecoration(
              color: AppTheme.primaryColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(AppTheme.borderRadiusSM),
            ),
            child: IconButton(
              icon: const Icon(Icons.refresh, size: 22),
              onPressed: _loadProjects,
              tooltip: '刷新',
              color: AppTheme.primaryColor,
            ),
          ),
        ],
      ),
      body:
          _isLoading
              ? Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CircularProgressIndicator(
                      color: AppTheme.primaryColor,
                      strokeWidth: 3,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      '加载中...',
                      style: TextStyle(
                        color:
                            isDarkMode
                                ? AppTheme.darkTextMediumColor
                                : AppTheme.textMediumColor,
                        fontSize: 16,
                      ),
                    ),
                  ],
                ),
              )
              : SingleChildScrollView(
                child: Column(
                children: [
                  // 功能卡片区域
                  Container(
                      padding: const EdgeInsets.all(20),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                          Text(
                          '功能工具',
                          style: TextStyle(
                              fontSize: 24,
                              fontWeight: FontWeight.w700,
                              color:
                                  isDarkMode
                                      ? AppTheme.darkTextColor
                                      : AppTheme.textDarkColor,
                              letterSpacing: -0.5,
                          ),
                        ),
                          const SizedBox(height: 8),
                          Text(
                            '选择合适的工具来创建引流内容',
                            style: TextStyle(
                              fontSize: 16,
                              color:
                                  isDarkMode
                                      ? AppTheme.darkTextMediumColor
                                      : AppTheme.textMediumColor,
                            ),
                          ),
                          const SizedBox(height: 24),
                        GridView.count(
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          crossAxisCount: 2,
                          crossAxisSpacing: 16,
                          mainAxisSpacing: 16,
                            childAspectRatio: 1.1,
                          children: [
                            _buildFunctionCard(
                              icon: Icons.auto_awesome,
                              title: '引流图片生成器',
                                subtitle: '生成方便在各平台使用的引流图片',
                                gradientColors: [
                                  AppTheme.purpleDark,
                                  AppTheme.purpleLight,
                                ],
                              onTap: () => _navigateToAdvancedImageGenerator(),
                              isNew: true,
                            ),
                            _buildFunctionCard(
                              icon: Icons.text_fields,
                              title: '文本转换',
                              subtitle: 'Emoji转换和字符干扰',
                                gradientColors: [
                                  AppTheme.greenDark,
                                  AppTheme.greenLight,
                                ],
                              onTap: () => _navigateToTextTransformer(),
                            ),
                            _buildFunctionCard(
                              icon: Icons.water_drop,
                              title: '水印处理',
                              subtitle: '添加和移除隐形水印',
                                gradientColors: [
                                  AppTheme.orangeDark,
                                  AppTheme.orangeLight,
                                ],
                              onTap: () => _navigateToWatermark(),
                            ),
                            _buildFunctionCard(
                              icon: Icons.add_circle,
                              title: '新建项目',
                              subtitle: '创建引流项目配置',
                                gradientColors: [
                                  AppTheme.primaryColor,
                                  AppTheme.secondaryColor,
                                ],
                              onTap: () => _createNewProject(),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),

                  // 项目列表区域
                    Container(
                      padding: const EdgeInsets.all(20),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            '我的项目',
                            style: TextStyle(
                              fontSize: 24,
                              fontWeight: FontWeight.w700,
                              color:
                                  isDarkMode
                                      ? AppTheme.darkTextColor
                                      : AppTheme.textDarkColor,
                              letterSpacing: -0.5,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            '管理您的引流项目配置',
                            style: TextStyle(
                              fontSize: 16,
                              color:
                                  isDarkMode
                                      ? AppTheme.darkTextMediumColor
                                      : AppTheme.textMediumColor,
                            ),
                          ),
                          const SizedBox(height: 24),
                          _projects.isEmpty
                              ? _buildEmptyState()
                              : _buildProjectList(),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
    );
  }

  Widget _buildFunctionCard({
    required IconData icon,
    required String title,
    required String subtitle,
    required List<Color> gradientColors,
    required VoidCallback onTap,
    bool isNew = false,
  }) {
    return TweenAnimationBuilder<double>(
      duration: const Duration(milliseconds: 300),
      tween: Tween(begin: 0.0, end: 1.0),
      builder: (context, value, child) {
        return Transform.scale(
          scale: 0.95 + (0.05 * value),
          child: Opacity(
            opacity: value,
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(AppTheme.borderRadiusLG),
                boxShadow: [
                  BoxShadow(
                    color: gradientColors.first.withValues(alpha: 0.3),
                    blurRadius: 12,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Material(
                color: Colors.transparent,
                borderRadius: BorderRadius.circular(AppTheme.borderRadiusLG),
                child: InkWell(
                  onTap: onTap,
                  borderRadius: BorderRadius.circular(AppTheme.borderRadiusLG),
                  child: Container(
                    padding: const EdgeInsets.all(20),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(
                        AppTheme.borderRadiusLG,
                      ),
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: gradientColors,
                      ),
                    ),
                    child: Stack(
                      children: [
                        Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(icon, size: 48, color: Colors.white),
                            const SizedBox(height: 16),
                            Text(
                              title,
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                                color: Colors.white,
                                letterSpacing: -0.2,
                              ),
                              textAlign: TextAlign.center,
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                            const SizedBox(height: 8),
                            Text(
                              subtitle,
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.white.withValues(alpha: 0.9),
                                height: 1.3,
                              ),
                              textAlign: TextAlign.center,
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ],
                        ),
                        if (isNew)
                          Positioned(
                            top: 8,
                            right: 8,
                            child: Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 4,
                              ),
                              decoration: BoxDecoration(
                                color: Colors.white.withValues(alpha: 0.2),
                                borderRadius: BorderRadius.circular(
                                  AppTheme.borderRadiusSM,
                                ),
                                border: Border.all(
                                  color: Colors.white.withValues(alpha: 0.3),
                                  width: 1,
                                ),
                              ),
                              child: const Text(
                                'NEW',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 10,
                                  fontWeight: FontWeight.w700,
                                  letterSpacing: 0.5,
                                ),
                              ),
                            ),
                          ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildEmptyState() {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Container(
      padding: const EdgeInsets.all(32),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 100,
            height: 100,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  AppTheme.primaryColor.withValues(alpha: 0.3),
                  AppTheme.secondaryColor.withValues(alpha: 0.3),
                ],
              ),
              borderRadius: BorderRadius.circular(AppTheme.borderRadiusXL),
            ),
            child: Icon(
              Icons.folder_open,
              size: 50,
              color:
                  isDarkMode
                      ? AppTheme.darkTextMediumColor
                      : AppTheme.textMediumColor,
            ),
          ),
          const SizedBox(height: 24),
          Text(
            '暂无项目',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.w600,
              color:
                  isDarkMode ? AppTheme.darkTextColor : AppTheme.textDarkColor,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '点击"新建项目"开始创建您的第一个引流项目',
            style: TextStyle(
              fontSize: 16,
              color:
                  isDarkMode
                      ? AppTheme.darkTextMediumColor
                      : AppTheme.textMediumColor,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 32),
          Container(
            decoration: BoxDecoration(
              gradient: AppTheme.primaryGradient,
              borderRadius: BorderRadius.circular(AppTheme.borderRadiusMD),
              boxShadow: [
                BoxShadow(
                  color: AppTheme.primaryColor.withValues(alpha: 0.3),
                  blurRadius: 12,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: ElevatedButton.icon(
              onPressed: _createNewProject,
              icon: const Icon(Icons.add, size: 20),
              label: const Text(
                '新建项目',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.transparent,
                foregroundColor: Colors.white,
                shadowColor: Colors.transparent,
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 16,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(AppTheme.borderRadiusMD),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProjectList() {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: _projects.length,
      itemBuilder: (context, index) {
        final project = _projects[index];
        return TweenAnimationBuilder<double>(
          duration: Duration(milliseconds: 300 + (index * 100)),
          tween: Tween(begin: 0.0, end: 1.0),
          builder: (context, value, child) {
            return Transform.translate(
              offset: Offset(0, 20 * (1 - value)),
              child: Opacity(
                opacity: value,
                child: Container(
                  margin: const EdgeInsets.only(bottom: 16),
                  decoration: BoxDecoration(
                    color:
                        isDarkMode
                            ? AppTheme.darkBgLightColor
                            : AppTheme.bgWhiteColor,
                    borderRadius: BorderRadius.circular(
                      AppTheme.borderRadiusLG,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color:
                            isDarkMode
                                ? Colors.black.withValues(alpha: 0.3)
                                : Colors.black.withValues(alpha: 0.08),
                        blurRadius: 12,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: Material(
                    color: Colors.transparent,
                    borderRadius: BorderRadius.circular(
                      AppTheme.borderRadiusLG,
                    ),
                    child: InkWell(
                      onTap: () => _editProject(project),
                      borderRadius: BorderRadius.circular(
                        AppTheme.borderRadiusLG,
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(20),
                        child: Row(
                          children: [
                            Container(
                              width: 56,
                              height: 56,
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                  begin: Alignment.topLeft,
                                  end: Alignment.bottomRight,
                                  colors: [
                                    AppTheme.primaryColor,
                                    AppTheme.secondaryColor,
                                  ],
                                ),
                                borderRadius: BorderRadius.circular(
                                  AppTheme.borderRadiusMD,
                                ),
                              ),
                              child: const Icon(
                                Icons.folder,
                                color: Colors.white,
                                size: 28,
                              ),
                            ),
                            const SizedBox(width: 16),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    project.name,
                                    style: TextStyle(
                                      fontWeight: FontWeight.w600,
                                      fontSize: 18,
                                      color:
                                          isDarkMode
                                              ? AppTheme.darkTextColor
                                              : AppTheme.textDarkColor,
                                      letterSpacing: -0.2,
                                    ),
                                  ),
                                  const SizedBox(height: 6),
                                  Text(
                                    project.description,
                                    style: TextStyle(
                                      fontSize: 14,
                                      color:
                                          isDarkMode
                                              ? AppTheme.darkTextMediumColor
                                              : AppTheme.textMediumColor,
                                      height: 1.3,
                                    ),
                                    maxLines: 2,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                  const SizedBox(height: 8),
                                  Row(
                                    children: [
                                      Icon(
                                        Icons.access_time,
                                        size: 14,
                                        color:
                                            isDarkMode
                                                ? AppTheme.darkTextLightColor
                                                : AppTheme.textLightColor,
                                      ),
                                      const SizedBox(width: 4),
                                      Text(
                                        '更新时间: ${_formatDate(project.updatedAt)}',
                                        style: TextStyle(
                                          fontSize: 12,
                                          color:
                                              isDarkMode
                                                  ? AppTheme.darkTextLightColor
                                                  : AppTheme.textLightColor,
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                            _buildProjectActionButton(project, isDarkMode),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildProjectActionButton(
    TrafficGuideProject project,
    bool isDarkMode,
  ) {
    return Container(
      decoration: BoxDecoration(
        color:
            isDarkMode
                ? AppTheme.darkBorderColor.withValues(alpha: 0.3)
                : AppTheme.borderColor.withValues(alpha: 0.5),
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusSM),
      ),
      child: PopupMenuButton<String>(
        onSelected: (value) => _handleProjectAction(value, project),
        icon: Icon(
          Icons.more_vert,
          color:
              isDarkMode
                  ? AppTheme.darkTextMediumColor
                  : AppTheme.textMediumColor,
          size: 20,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppTheme.borderRadiusMD),
        ),
        itemBuilder:
            (context) => [
              PopupMenuItem(
                value: 'edit',
                child: Row(
                  children: [
                    Icon(
                      Icons.edit_outlined,
                      size: 18,
                      color: AppTheme.primaryColor,
                    ),
                    const SizedBox(width: 12),
                    const Text('编辑'),
                  ],
                ),
              ),
              PopupMenuItem(
                value: 'delete',
                child: Row(
                  children: [
                    Icon(
                      Icons.delete_outline,
                      size: 18,
                      color: AppTheme.redDark,
                    ),
                    const SizedBox(width: 12),
                    Text('删除', style: TextStyle(color: AppTheme.redDark)),
                  ],
                ),
              ),
            ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }



  void _navigateToAdvancedImageGenerator() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const AdvancedImageGeneratorScreen(),
      ),
    );
  }

  void _navigateToTextTransformer() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const TextTransformerScreen()),
    );
  }

  void _navigateToWatermark() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const WatermarkScreen()),
    );
  }

  void _createNewProject() {
    final uuid = const Uuid();
    final newProject = TrafficGuideProject(
      id: uuid.v4(),
      name: '新项目',
      description: '引流项目配置',
      imageConfig: TrafficImageConfig(text: ''),
      textConfig: TextTransformConfig(),
      watermarkConfig: WatermarkConfig(text: ''),
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ProjectEditorScreen(project: newProject),
      ),
    ).then((_) => _loadProjects());
  }

  void _editProject(TrafficGuideProject project) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ProjectEditorScreen(project: project),
      ),
    ).then((_) => _loadProjects());
  }

  void _handleProjectAction(String action, TrafficGuideProject project) {
    switch (action) {
      case 'edit':
        _editProject(project);
        break;
      case 'delete':
        _deleteProject(project);
        break;
    }
  }

  Future<void> _deleteProject(TrafficGuideProject project) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('确认删除'),
            content: Text('确定要删除项目"${project.name}"吗？'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context, false),
                child: const Text('取消'),
              ),
              TextButton(
                onPressed: () => Navigator.pop(context, true),
                style: TextButton.styleFrom(foregroundColor: Colors.red),
                child: const Text('删除'),
              ),
            ],
          ),
    );

    if (confirmed == true) {
      // 这里应该调用服务删除项目
      setState(() {
        _projects.removeWhere((p) => p.id == project.id);
      });
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('项目"${project.name}"已删除')));
    }
  }
}
