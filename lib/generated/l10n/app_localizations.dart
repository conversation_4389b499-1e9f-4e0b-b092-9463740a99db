import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart' as intl;

import 'app_localizations_en.dart';
import 'app_localizations_ja.dart';
import 'app_localizations_zh.dart';

// ignore_for_file: type=lint

/// Callers can lookup localized strings with an instance of AppLocalizations
/// returned by `AppLocalizations.of(context)`.
///
/// Applications need to include `AppLocalizations.delegate()` in their app's
/// `localizationDelegates` list, and the locales they support in the app's
/// `supportedLocales` list. For example:
///
/// ```dart
/// import 'l10n/app_localizations.dart';
///
/// return MaterialApp(
///   localizationsDelegates: AppLocalizations.localizationsDelegates,
///   supportedLocales: AppLocalizations.supportedLocales,
///   home: MyApplicationHome(),
/// );
/// ```
///
/// ## Update pubspec.yaml
///
/// Please make sure to update your pubspec.yaml to include the following
/// packages:
///
/// ```yaml
/// dependencies:
///   # Internationalization support.
///   flutter_localizations:
///     sdk: flutter
///   intl: any # Use the pinned version from flutter_localizations
///
///   # Rest of dependencies
/// ```
///
/// ## iOS Applications
///
/// iOS applications define key application metadata, including supported
/// locales, in an Info.plist file that is built into the application bundle.
/// To configure the locales supported by your app, you’ll need to edit this
/// file.
///
/// First, open your project’s ios/Runner.xcworkspace Xcode workspace file.
/// Then, in the Project Navigator, open the Info.plist file under the Runner
/// project’s Runner folder.
///
/// Next, select the Information Property List item, select Add Item from the
/// Editor menu, then select Localizations from the pop-up menu.
///
/// Select and expand the newly-created Localizations item then, for each
/// locale your application supports, add a new item and select the locale
/// you wish to add from the pop-up menu in the Value field. This list should
/// be consistent with the languages listed in the AppLocalizations.supportedLocales
/// property.
abstract class AppLocalizations {
  AppLocalizations(String locale)
    : localeName = intl.Intl.canonicalizedLocale(locale.toString());

  final String localeName;

  static AppLocalizations of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations)!;
  }

  static const LocalizationsDelegate<AppLocalizations> delegate =
      _AppLocalizationsDelegate();

  /// A list of this localizations delegate along with the default localizations
  /// delegates.
  ///
  /// Returns a list of localizations delegates containing this delegate along with
  /// GlobalMaterialLocalizations.delegate, GlobalCupertinoLocalizations.delegate,
  /// and GlobalWidgetsLocalizations.delegate.
  ///
  /// Additional delegates can be added by appending to this list in
  /// MaterialApp. This list does not have to be used at all if a custom list
  /// of delegates is preferred or required.
  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates =
      <LocalizationsDelegate<dynamic>>[
        delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
      ];

  /// A list of this localizations delegate's supported locales.
  static const List<Locale> supportedLocales = <Locale>[
    Locale('en'),
    Locale('ja'),
    Locale('zh'),
  ];

  /// The name of the application
  ///
  /// In en, this message translates to:
  /// **'ContentPal'**
  String get appName;

  /// The Chinese name of the application
  ///
  /// In en, this message translates to:
  /// **'内容君'**
  String get appNameChinese;

  /// Description of the application
  ///
  /// In en, this message translates to:
  /// **'Professional content processing tool that makes content creation easier'**
  String get appDescription;

  /// Home page title
  ///
  /// In en, this message translates to:
  /// **'Home'**
  String get home;

  /// Settings page title
  ///
  /// In en, this message translates to:
  /// **'Settings'**
  String get settings;

  /// Language setting label
  ///
  /// In en, this message translates to:
  /// **'Language'**
  String get language;

  /// Theme setting label
  ///
  /// In en, this message translates to:
  /// **'Theme'**
  String get theme;

  /// Light theme option
  ///
  /// In en, this message translates to:
  /// **'Light'**
  String get lightTheme;

  /// Dark theme option
  ///
  /// In en, this message translates to:
  /// **'Dark'**
  String get darkTheme;

  /// System theme option
  ///
  /// In en, this message translates to:
  /// **'System'**
  String get systemTheme;

  /// Markdown module name
  ///
  /// In en, this message translates to:
  /// **'Markdown'**
  String get markdown;

  /// Text Cards module name
  ///
  /// In en, this message translates to:
  /// **'Text Cards'**
  String get textCards;

  /// PDF module name
  ///
  /// In en, this message translates to:
  /// **'PDF'**
  String get pdf;

  /// Voice module name
  ///
  /// In en, this message translates to:
  /// **'Voice'**
  String get voice;

  /// HTML module name
  ///
  /// In en, this message translates to:
  /// **'HTML'**
  String get html;

  /// SVG module name
  ///
  /// In en, this message translates to:
  /// **'SVG'**
  String get svg;

  /// Content module name
  ///
  /// In en, this message translates to:
  /// **'Content'**
  String get content;

  /// Traffic Guide module name
  ///
  /// In en, this message translates to:
  /// **'Traffic Guide'**
  String get trafficGuide;

  /// Create button text
  ///
  /// In en, this message translates to:
  /// **'Create'**
  String get create;

  /// Edit button text
  ///
  /// In en, this message translates to:
  /// **'Edit'**
  String get edit;

  /// Delete button text
  ///
  /// In en, this message translates to:
  /// **'Delete'**
  String get delete;

  /// Save button text
  ///
  /// In en, this message translates to:
  /// **'Save'**
  String get save;

  /// Cancel button text
  ///
  /// In en, this message translates to:
  /// **'Cancel'**
  String get cancel;

  /// Confirm button text
  ///
  /// In en, this message translates to:
  /// **'Confirm'**
  String get confirm;

  /// Yes button text
  ///
  /// In en, this message translates to:
  /// **'Yes'**
  String get yes;

  /// No button text
  ///
  /// In en, this message translates to:
  /// **'No'**
  String get no;

  /// OK button text
  ///
  /// In en, this message translates to:
  /// **'OK'**
  String get ok;

  /// Loading indicator text
  ///
  /// In en, this message translates to:
  /// **'Loading...'**
  String get loading;

  /// Error message prefix
  ///
  /// In en, this message translates to:
  /// **'Error'**
  String get error;

  /// Success message prefix
  ///
  /// In en, this message translates to:
  /// **'Success'**
  String get success;

  /// Warning message prefix
  ///
  /// In en, this message translates to:
  /// **'Warning'**
  String get warning;

  /// Info message prefix
  ///
  /// In en, this message translates to:
  /// **'Info'**
  String get info;

  /// Search placeholder text
  ///
  /// In en, this message translates to:
  /// **'Search'**
  String get search;

  /// Search input hint text
  ///
  /// In en, this message translates to:
  /// **'Enter search terms...'**
  String get searchHint;

  /// No search results message
  ///
  /// In en, this message translates to:
  /// **'No results found'**
  String get noResults;

  /// Try again button text
  ///
  /// In en, this message translates to:
  /// **'Try Again'**
  String get tryAgain;

  /// Refresh button text
  ///
  /// In en, this message translates to:
  /// **'Refresh'**
  String get refresh;

  /// Share button text
  ///
  /// In en, this message translates to:
  /// **'Share'**
  String get share;

  /// Export button text
  ///
  /// In en, this message translates to:
  /// **'Export'**
  String get export;

  /// Import button text
  ///
  /// In en, this message translates to:
  /// **'Import'**
  String get import;

  /// Copy button text
  ///
  /// In en, this message translates to:
  /// **'Copy'**
  String get copy;

  /// Paste button text
  ///
  /// In en, this message translates to:
  /// **'Paste'**
  String get paste;

  /// Cut button text
  ///
  /// In en, this message translates to:
  /// **'Cut'**
  String get cut;

  /// Undo button text
  ///
  /// In en, this message translates to:
  /// **'Undo'**
  String get undo;

  /// Redo button text
  ///
  /// In en, this message translates to:
  /// **'Redo'**
  String get redo;

  /// Select all button text
  ///
  /// In en, this message translates to:
  /// **'Select All'**
  String get selectAll;

  /// Close button text
  ///
  /// In en, this message translates to:
  /// **'Close'**
  String get close;

  /// Back button text
  ///
  /// In en, this message translates to:
  /// **'Back'**
  String get back;

  /// Next button text
  ///
  /// In en, this message translates to:
  /// **'Next'**
  String get next;

  /// Previous button text
  ///
  /// In en, this message translates to:
  /// **'Previous'**
  String get previous;

  /// Done button text
  ///
  /// In en, this message translates to:
  /// **'Done'**
  String get done;

  /// Finish button text
  ///
  /// In en, this message translates to:
  /// **'Finish'**
  String get finish;

  /// Skip button text
  ///
  /// In en, this message translates to:
  /// **'Skip'**
  String get skip;

  /// Continue button text
  ///
  /// In en, this message translates to:
  /// **'Continue'**
  String get continueAction;

  /// Retry button text
  ///
  /// In en, this message translates to:
  /// **'Retry'**
  String get retry;

  /// Reset button text
  ///
  /// In en, this message translates to:
  /// **'Reset'**
  String get reset;

  /// Clear button text
  ///
  /// In en, this message translates to:
  /// **'Clear'**
  String get clear;

  /// Apply button text
  ///
  /// In en, this message translates to:
  /// **'Apply'**
  String get apply;

  /// Preview button text
  ///
  /// In en, this message translates to:
  /// **'Preview'**
  String get preview;

  /// Download button text
  ///
  /// In en, this message translates to:
  /// **'Download'**
  String get download;

  /// Upload button text
  ///
  /// In en, this message translates to:
  /// **'Upload'**
  String get upload;

  /// File label
  ///
  /// In en, this message translates to:
  /// **'File'**
  String get file;

  /// Folder label
  ///
  /// In en, this message translates to:
  /// **'Folder'**
  String get folder;

  /// Name field label
  ///
  /// In en, this message translates to:
  /// **'Name'**
  String get name;

  /// Title field label
  ///
  /// In en, this message translates to:
  /// **'Title'**
  String get title;

  /// Description field label
  ///
  /// In en, this message translates to:
  /// **'Description'**
  String get description;

  /// Size field label
  ///
  /// In en, this message translates to:
  /// **'Size'**
  String get size;

  /// Date field label
  ///
  /// In en, this message translates to:
  /// **'Date'**
  String get date;

  /// Time field label
  ///
  /// In en, this message translates to:
  /// **'Time'**
  String get time;

  /// Type field label
  ///
  /// In en, this message translates to:
  /// **'Type'**
  String get type;

  /// Status field label
  ///
  /// In en, this message translates to:
  /// **'Status'**
  String get status;

  /// Version field label
  ///
  /// In en, this message translates to:
  /// **'Version'**
  String get version;

  /// Author field label
  ///
  /// In en, this message translates to:
  /// **'Author'**
  String get author;

  /// Tags field label
  ///
  /// In en, this message translates to:
  /// **'Tags'**
  String get tags;

  /// Category field label
  ///
  /// In en, this message translates to:
  /// **'Category'**
  String get category;

  /// Priority field label
  ///
  /// In en, this message translates to:
  /// **'Priority'**
  String get priority;

  /// High priority
  ///
  /// In en, this message translates to:
  /// **'High'**
  String get high;

  /// Medium priority
  ///
  /// In en, this message translates to:
  /// **'Medium'**
  String get medium;

  /// Low priority
  ///
  /// In en, this message translates to:
  /// **'Low'**
  String get low;

  /// Enabled status
  ///
  /// In en, this message translates to:
  /// **'Enabled'**
  String get enabled;

  /// Disabled status
  ///
  /// In en, this message translates to:
  /// **'Disabled'**
  String get disabled;

  /// Online status
  ///
  /// In en, this message translates to:
  /// **'Online'**
  String get online;

  /// Offline status
  ///
  /// In en, this message translates to:
  /// **'Offline'**
  String get offline;

  /// Connected status
  ///
  /// In en, this message translates to:
  /// **'Connected'**
  String get connected;

  /// Disconnected status
  ///
  /// In en, this message translates to:
  /// **'Disconnected'**
  String get disconnected;

  /// Available status
  ///
  /// In en, this message translates to:
  /// **'Available'**
  String get available;

  /// Unavailable status
  ///
  /// In en, this message translates to:
  /// **'Unavailable'**
  String get unavailable;

  /// Active status
  ///
  /// In en, this message translates to:
  /// **'Active'**
  String get active;

  /// Inactive status
  ///
  /// In en, this message translates to:
  /// **'Inactive'**
  String get inactive;

  /// Public visibility
  ///
  /// In en, this message translates to:
  /// **'Public'**
  String get public;

  /// Private visibility
  ///
  /// In en, this message translates to:
  /// **'Private'**
  String get private;

  /// Draft status
  ///
  /// In en, this message translates to:
  /// **'Draft'**
  String get draft;

  /// Published status
  ///
  /// In en, this message translates to:
  /// **'Published'**
  String get published;

  /// Archived status
  ///
  /// In en, this message translates to:
  /// **'Archived'**
  String get archived;

  /// PDF module title
  ///
  /// In en, this message translates to:
  /// **'PDF Professional Tool'**
  String get pdfProfessionalTool;

  /// PDF module description
  ///
  /// In en, this message translates to:
  /// **'Powerful PDF processing capabilities that make document management easier'**
  String get pdfToolDescription;

  /// PDF security feature title
  ///
  /// In en, this message translates to:
  /// **'Security Encryption'**
  String get securityEncryption;

  /// PDF security feature description
  ///
  /// In en, this message translates to:
  /// **'Password Protection\nPermission Control'**
  String get passwordProtectionPermissionControl;

  /// PDF annotation feature title
  ///
  /// In en, this message translates to:
  /// **'Intelligent Annotation'**
  String get intelligentAnnotation;

  /// PDF annotation feature description
  ///
  /// In en, this message translates to:
  /// **'Highlight Marking\nText Annotation'**
  String get highlightMarkingTextAnnotation;

  /// PDF search feature title
  ///
  /// In en, this message translates to:
  /// **'Quick Search'**
  String get quickSearch;

  /// PDF search feature description
  ///
  /// In en, this message translates to:
  /// **'Full-text Search\nContent Location'**
  String get fullTextSearchContentLocation;

  /// PDF sharing feature title
  ///
  /// In en, this message translates to:
  /// **'Convenient Sharing'**
  String get convenientSharing;

  /// PDF sharing feature description
  ///
  /// In en, this message translates to:
  /// **'Multiple Formats\nOne-click Export'**
  String get multipleFormatsOneClickExport;

  /// Welcome message for PDF tool
  ///
  /// In en, this message translates to:
  /// **'Welcome to PDF Professional Tool!'**
  String get welcomeToPdfTool;

  /// Import PDF button text
  ///
  /// In en, this message translates to:
  /// **'Import First PDF Document'**
  String get importFirstPdfDocument;

  /// Appearance settings section title
  ///
  /// In en, this message translates to:
  /// **'Appearance'**
  String get appearance;

  /// Follow system theme option
  ///
  /// In en, this message translates to:
  /// **'Follow System'**
  String get followSystem;

  /// Language change notification message
  ///
  /// In en, this message translates to:
  /// **'Language changes will take effect immediately'**
  String get languageChangeEffect;

  /// Content library title
  ///
  /// In en, this message translates to:
  /// **'Content Library'**
  String get contentLibrary;

  /// Content library subtitle
  ///
  /// In en, this message translates to:
  /// **'Manage All Cards'**
  String get manageAllCards;

  /// Template library title
  ///
  /// In en, this message translates to:
  /// **'Template Library'**
  String get templateLibrary;

  /// Template library subtitle
  ///
  /// In en, this message translates to:
  /// **'Browse Beautiful Templates'**
  String get browseBeautifulTemplates;

  /// Text splitter input title
  ///
  /// In en, this message translates to:
  /// **'Input text to split'**
  String get inputTextToSplit;

  /// Text splitter input description
  ///
  /// In en, this message translates to:
  /// **'Paste or input long text content, the system will intelligently recognize and split it into multiple cards'**
  String get pasteOrInputLongText;

  /// Paste from clipboard button
  ///
  /// In en, this message translates to:
  /// **'Paste Clipboard'**
  String get pasteClipboard;

  /// Clear content button
  ///
  /// In en, this message translates to:
  /// **'Clear Content'**
  String get clearContent;

  /// Default card title with number
  ///
  /// In en, this message translates to:
  /// **'Card {number}'**
  String cardNumber(int number);

  /// Loading demo data message
  ///
  /// In en, this message translates to:
  /// **'Loading demo data...'**
  String get loadingDemoData;

  /// Feature description for content library
  ///
  /// In en, this message translates to:
  /// **'✨ Modern UI Design\n🖼️ Render Result Preview\n📱 Block Mode Support\n⚡ High Performance Experience'**
  String get modernUIDesign;

  /// Edit function message
  ///
  /// In en, this message translates to:
  /// **'Edit function: {title}'**
  String editFunction(String title);

  /// Deleted item message
  ///
  /// In en, this message translates to:
  /// **'Deleted: {title}'**
  String deleted(String title);

  /// Share function message
  ///
  /// In en, this message translates to:
  /// **'Share function: {title}'**
  String shareFunction(String title);

  /// Create new content title
  ///
  /// In en, this message translates to:
  /// **'Create New Content'**
  String get createNewContent;

  /// Create content type selection description
  ///
  /// In en, this message translates to:
  /// **'Select the type of content you want to create'**
  String get selectContentType;

  /// Bold markdown syntax
  ///
  /// In en, this message translates to:
  /// **'**Bold**'**
  String get bold;

  /// Italic markdown syntax
  ///
  /// In en, this message translates to:
  /// **'*Italic*'**
  String get italic;

  /// Heading 1 markdown syntax
  ///
  /// In en, this message translates to:
  /// **'# Heading 1'**
  String get heading1;

  /// Heading 2 markdown syntax
  ///
  /// In en, this message translates to:
  /// **'## Heading 2'**
  String get heading2;

  /// Heading 3 markdown syntax
  ///
  /// In en, this message translates to:
  /// **'### Heading 3'**
  String get heading3;

  /// List markdown syntax
  ///
  /// In en, this message translates to:
  /// **'- List item\n- List item'**
  String get list;

  /// Link markdown syntax
  ///
  /// In en, this message translates to:
  /// **'[Link text](URL)'**
  String get link;

  /// Image markdown syntax
  ///
  /// In en, this message translates to:
  /// **'![Image description](Image URL)'**
  String get image;

  /// Inline code markdown syntax
  ///
  /// In en, this message translates to:
  /// **'`Code`'**
  String get code;

  /// Code block markdown syntax
  ///
  /// In en, this message translates to:
  /// **'```\nCode block\n```'**
  String get codeBlock;

  /// Quote markdown syntax
  ///
  /// In en, this message translates to:
  /// **'> Quote text'**
  String get quote;

  /// Table markdown syntax
  ///
  /// In en, this message translates to:
  /// **'| Column 1 | Column 2 |\n| --- | --- |\n| Content 1 | Content 2 |'**
  String get table;

  /// My content library title
  ///
  /// In en, this message translates to:
  /// **'My Content Library'**
  String get myContentLibrary;

  /// Content library description
  ///
  /// In en, this message translates to:
  /// **'Manage and browse all your content'**
  String get manageAndBrowseContent;

  /// Content tools section title
  ///
  /// In en, this message translates to:
  /// **'Content Tools'**
  String get contentTools;

  /// Recommended tools section title
  ///
  /// In en, this message translates to:
  /// **'Recommended Tools'**
  String get recommendedTools;

  /// Markdown tool title
  ///
  /// In en, this message translates to:
  /// **'Markdown'**
  String get markdownTitle;

  /// Markdown tool description
  ///
  /// In en, this message translates to:
  /// **'Document editing and rendering'**
  String get markdownDescription;

  /// Text cards tool title
  ///
  /// In en, this message translates to:
  /// **'Text Cards'**
  String get textCardsTitle;

  /// Text cards tool description
  ///
  /// In en, this message translates to:
  /// **'Knowledge card customization and rendering'**
  String get textCardsDescription;

  /// Traffic guide tool title
  ///
  /// In en, this message translates to:
  /// **'Traffic Guide'**
  String get trafficGuideTitle;

  /// Traffic guide tool description
  ///
  /// In en, this message translates to:
  /// **'Traffic image and text processing'**
  String get trafficGuideDescription;

  /// File tools section title
  ///
  /// In en, this message translates to:
  /// **'File Tools'**
  String get fileTools;

  /// SVG tool title
  ///
  /// In en, this message translates to:
  /// **'SVG'**
  String get svgTitle;

  /// SVG tool description
  ///
  /// In en, this message translates to:
  /// **'Vector graphics processing'**
  String get svgDescription;

  /// HTML tool title
  ///
  /// In en, this message translates to:
  /// **'HTML'**
  String get htmlTitle;

  /// HTML tool description
  ///
  /// In en, this message translates to:
  /// **'Web content editing'**
  String get htmlDescription;

  /// Loading content message
  ///
  /// In en, this message translates to:
  /// **'Loading content...'**
  String get loadingContent;

  /// Language change success message
  ///
  /// In en, this message translates to:
  /// **'Language changed to {language}'**
  String languageChangedTo(String language);

  /// Developer settings section title
  ///
  /// In en, this message translates to:
  /// **'Developer'**
  String get developer;

  /// Content library demo title
  ///
  /// In en, this message translates to:
  /// **'Content Library Demo'**
  String get contentLibraryDemo;

  /// Content library demo description
  ///
  /// In en, this message translates to:
  /// **'View new content library features'**
  String get viewNewContentLibraryFeatures;

  /// I18n demo title
  ///
  /// In en, this message translates to:
  /// **'Internationalization Demo'**
  String get i18nDemo;

  /// I18n demo description
  ///
  /// In en, this message translates to:
  /// **'View multi-language support effects'**
  String get viewMultiLanguageSupport;

  /// About settings section title
  ///
  /// In en, this message translates to:
  /// **'About'**
  String get about;

  /// Version info title
  ///
  /// In en, this message translates to:
  /// **'Version Info'**
  String get versionInfo;

  /// Help and feedback title
  ///
  /// In en, this message translates to:
  /// **'Help & Feedback'**
  String get helpAndFeedback;

  /// Help and feedback description
  ///
  /// In en, this message translates to:
  /// **'Get help or provide feedback'**
  String get getHelpOrProvideFeedback;

  /// Help and feedback dialog content
  ///
  /// In en, this message translates to:
  /// **'If you have any questions or suggestions, please contact us through the following methods:\n\nEmail: <EMAIL>'**
  String get helpAndFeedbackContent;

  /// Select theme dialog title
  ///
  /// In en, this message translates to:
  /// **'Select Theme'**
  String get selectTheme;

  /// Light theme mode
  ///
  /// In en, this message translates to:
  /// **'Light Mode'**
  String get lightMode;

  /// Dark theme mode
  ///
  /// In en, this message translates to:
  /// **'Dark Mode'**
  String get darkMode;

  /// System theme mode
  ///
  /// In en, this message translates to:
  /// **'Follow System'**
  String get systemMode;

  /// Settings page subtitle
  ///
  /// In en, this message translates to:
  /// **'Personalize your app experience'**
  String get personalizeYourAppExperience;
}

class _AppLocalizationsDelegate
    extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  Future<AppLocalizations> load(Locale locale) {
    return SynchronousFuture<AppLocalizations>(lookupAppLocalizations(locale));
  }

  @override
  bool isSupported(Locale locale) =>
      <String>['en', 'ja', 'zh'].contains(locale.languageCode);

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}

AppLocalizations lookupAppLocalizations(Locale locale) {
  // Lookup logic when only language code is specified.
  switch (locale.languageCode) {
    case 'en':
      return AppLocalizationsEn();
    case 'ja':
      return AppLocalizationsJa();
    case 'zh':
      return AppLocalizationsZh();
  }

  throw FlutterError(
    'AppLocalizations.delegate failed to load unsupported locale "$locale". This is likely '
    'an issue with the localizations generation tool. Please file an issue '
    'on GitHub with a reproducible sample app and the gen-l10n configuration '
    'that was used.',
  );
}
