// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Japanese (`ja`).
class AppLocalizationsJa extends AppLocalizations {
  AppLocalizationsJa([String locale = 'ja']) : super(locale);

  @override
  String get appName => 'ContentPal';

  @override
  String get appNameChinese => '内容君';

  @override
  String get appDescription => 'プロフェッショナルなコンテンツ処理ツール、コンテンツ作成をより簡単に';

  @override
  String get home => 'ホーム';

  @override
  String get settings => '設定';

  @override
  String get language => '言語';

  @override
  String get theme => 'テーマ';

  @override
  String get lightTheme => 'ライト';

  @override
  String get darkTheme => 'ダーク';

  @override
  String get systemTheme => 'システム';

  @override
  String get markdown => 'Markdown';

  @override
  String get textCards => 'テキストカード';

  @override
  String get pdf => 'PDF';

  @override
  String get voice => '音声';

  @override
  String get html => 'HTML';

  @override
  String get svg => 'SVG';

  @override
  String get content => 'コンテンツ';

  @override
  String get trafficGuide => '交通ガイド';

  @override
  String get create => '作成';

  @override
  String get edit => '編集';

  @override
  String get delete => '削除';

  @override
  String get save => '保存';

  @override
  String get cancel => 'キャンセル';

  @override
  String get confirm => '確認';

  @override
  String get yes => 'はい';

  @override
  String get no => 'いいえ';

  @override
  String get ok => 'OK';

  @override
  String get loading => '読み込み中...';

  @override
  String get error => 'エラー';

  @override
  String get success => '成功';

  @override
  String get warning => '警告';

  @override
  String get info => '情報';

  @override
  String get search => '検索';

  @override
  String get searchHint => '検索内容を入力してください...';

  @override
  String get noResults => '結果が見つかりません';

  @override
  String get tryAgain => '再試行';

  @override
  String get refresh => '更新';

  @override
  String get share => '共有';

  @override
  String get export => 'エクスポート';

  @override
  String get import => 'インポート';

  @override
  String get copy => 'コピー';

  @override
  String get paste => '貼り付け';

  @override
  String get cut => '切り取り';

  @override
  String get undo => '元に戻す';

  @override
  String get redo => 'やり直し';

  @override
  String get selectAll => 'すべて選択';

  @override
  String get close => '閉じる';

  @override
  String get back => '戻る';

  @override
  String get next => '次へ';

  @override
  String get previous => '前へ';

  @override
  String get done => '完了';

  @override
  String get finish => '終了';

  @override
  String get skip => 'スキップ';

  @override
  String get continueAction => '続行';

  @override
  String get retry => '再試行';

  @override
  String get reset => 'リセット';

  @override
  String get clear => 'クリア';

  @override
  String get apply => '適用';

  @override
  String get preview => 'プレビュー';

  @override
  String get download => 'ダウンロード';

  @override
  String get upload => 'アップロード';

  @override
  String get file => 'ファイル';

  @override
  String get folder => 'フォルダ';

  @override
  String get name => '名前';

  @override
  String get title => 'タイトル';

  @override
  String get description => '説明';

  @override
  String get size => 'サイズ';

  @override
  String get date => '日付';

  @override
  String get time => '時間';

  @override
  String get type => 'タイプ';

  @override
  String get status => 'ステータス';

  @override
  String get version => 'バージョン';

  @override
  String get author => '作成者';

  @override
  String get tags => 'タグ';

  @override
  String get category => 'カテゴリ';

  @override
  String get priority => '優先度';

  @override
  String get high => '高';

  @override
  String get medium => '中';

  @override
  String get low => '低';

  @override
  String get enabled => '有効';

  @override
  String get disabled => '無効';

  @override
  String get online => 'オンライン';

  @override
  String get offline => 'オフライン';

  @override
  String get connected => '接続済み';

  @override
  String get disconnected => '切断済み';

  @override
  String get available => '利用可能';

  @override
  String get unavailable => '利用不可';

  @override
  String get active => 'アクティブ';

  @override
  String get inactive => '非アクティブ';

  @override
  String get public => '公開';

  @override
  String get private => '非公開';

  @override
  String get draft => '下書き';

  @override
  String get published => '公開済み';

  @override
  String get archived => 'アーカイブ済み';

  @override
  String get pdfProfessionalTool => 'PDFプロフェッショナルツール';

  @override
  String get pdfToolDescription => '強力なPDF処理機能で、文書管理をより簡単に';

  @override
  String get securityEncryption => 'セキュリティ暗号化';

  @override
  String get passwordProtectionPermissionControl => 'パスワード保護\n権限制御';

  @override
  String get intelligentAnnotation => 'インテリジェント注釈';

  @override
  String get highlightMarkingTextAnnotation => 'ハイライト\nテキスト注釈';

  @override
  String get quickSearch => 'クイック検索';

  @override
  String get fullTextSearchContentLocation => '全文検索\nコンテンツ位置';

  @override
  String get convenientSharing => '便利な共有';

  @override
  String get multipleFormatsOneClickExport => '複数フォーマット\nワンクリックエクスポート';

  @override
  String get welcomeToPdfTool => 'PDFプロフェッショナルツールへようこそ！';

  @override
  String get importFirstPdfDocument => '最初のPDF文書をインポート';

  @override
  String get appearance => '外観';

  @override
  String get followSystem => 'システムに従う';

  @override
  String get languageChangeEffect => '言語の変更は即座に有効になります';

  @override
  String get contentLibrary => 'コンテンツライブラリ';

  @override
  String get manageAllCards => 'すべてのカードを管理';

  @override
  String get templateLibrary => 'テンプレートライブラリ';

  @override
  String get browseBeautifulTemplates => '美しいテンプレートを閲覧';

  @override
  String get inputTextToSplit => '分割するテキストを入力';

  @override
  String get pasteOrInputLongText =>
      '長いテキストコンテンツを貼り付けまたは入力してください。システムが自動的に認識し、複数のカードに分割します';

  @override
  String get pasteClipboard => 'クリップボードから貼り付け';

  @override
  String get clearContent => 'コンテンツをクリア';

  @override
  String cardNumber(int number) {
    return 'カード $number';
  }

  @override
  String get loadingDemoData => 'デモデータを読み込み中...';

  @override
  String get modernUIDesign =>
      '✨ モダンUIデザイン\n🖼️ レンダリング結果プレビュー\n📱 ブロックモードサポート\n⚡ 高性能体験';

  @override
  String editFunction(String title) {
    return '編集機能：$title';
  }

  @override
  String deleted(String title) {
    return '削除されました：$title';
  }

  @override
  String shareFunction(String title) {
    return '共有機能：$title';
  }

  @override
  String get createNewContent => '新しいコンテンツを作成';

  @override
  String get selectContentType => '作成するコンテンツの種類を選択してください';

  @override
  String get bold => '**太字**';

  @override
  String get italic => '*斜体*';

  @override
  String get heading1 => '# 見出し1';

  @override
  String get heading2 => '## 見出し2';

  @override
  String get heading3 => '### 見出し3';

  @override
  String get list => '- リスト項目\n- リスト項目';

  @override
  String get link => '[リンクテキスト](URL)';

  @override
  String get image => '![画像説明](画像URL)';

  @override
  String get code => '`コード`';

  @override
  String get codeBlock => '```\nコードブロック\n```';

  @override
  String get quote => '> 引用テキスト';

  @override
  String get table => '| 列1 | 列2 |\n| --- | --- |\n| 内容1 | 内容2 |';

  @override
  String get myContentLibrary => 'マイコンテンツライブラリ';

  @override
  String get manageAndBrowseContent => 'すべてのコンテンツを管理・閲覧';

  @override
  String get contentTools => 'コンテンツツール';

  @override
  String get recommendedTools => 'おすすめツール';

  @override
  String get markdownTitle => 'Markdown';

  @override
  String get markdownDescription => 'ドキュメント編集とレンダリング';

  @override
  String get textCardsTitle => 'テキストカード';

  @override
  String get textCardsDescription => 'ナレッジカードのカスタマイズとレンダリング';

  @override
  String get trafficGuideTitle => 'トラフィックガイド';

  @override
  String get trafficGuideDescription => 'トラフィック画像とテキスト処理';

  @override
  String get fileTools => 'ファイルツール';

  @override
  String get svgTitle => 'SVG';

  @override
  String get svgDescription => 'ベクターグラフィック処理';

  @override
  String get htmlTitle => 'HTML';

  @override
  String get htmlDescription => 'ウェブコンテンツ編集';

  @override
  String get loadingContent => 'コンテンツを読み込み中...';

  @override
  String languageChangedTo(String language) {
    return '言語が$languageに変更されました';
  }

  @override
  String get developer => '開発者';

  @override
  String get contentLibraryDemo => 'コンテンツライブラリデモ';

  @override
  String get viewNewContentLibraryFeatures => '新しいコンテンツライブラリ機能を表示';

  @override
  String get i18nDemo => '国際化デモ';

  @override
  String get viewMultiLanguageSupport => '多言語サポート効果を表示';

  @override
  String get about => 'について';

  @override
  String get versionInfo => 'バージョン情報';

  @override
  String get helpAndFeedback => 'ヘルプとフィードバック';

  @override
  String get getHelpOrProvideFeedback => 'ヘルプを取得またはフィードバックを提供';

  @override
  String get helpAndFeedbackContent =>
      'ご質問やご提案がございましたら、以下の方法でお問い合わせください：\n\nメール：<EMAIL>';

  @override
  String get selectTheme => 'テーマを選択';

  @override
  String get lightMode => 'ライトモード';

  @override
  String get darkMode => 'ダークモード';

  @override
  String get systemMode => 'システムに従う';

  @override
  String get personalizeYourAppExperience => 'アプリ体験をパーソナライズ';
}
