// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Chinese (`zh`).
class AppLocalizationsZh extends AppLocalizations {
  AppLocalizationsZh([String locale = 'zh']) : super(locale);

  @override
  String get appName => 'ContentPal';

  @override
  String get appNameChinese => '内容君';

  @override
  String get appDescription => '专业的内容处理工具，让内容创作更轻松';

  @override
  String get home => '首页';

  @override
  String get settings => '设置';

  @override
  String get language => '语言';

  @override
  String get theme => '主题';

  @override
  String get lightTheme => '浅色';

  @override
  String get darkTheme => '深色';

  @override
  String get systemTheme => '跟随系统';

  @override
  String get markdown => 'Markdown';

  @override
  String get textCards => '文本卡片';

  @override
  String get pdf => 'PDF';

  @override
  String get voice => '语音';

  @override
  String get html => 'HTML';

  @override
  String get svg => 'SVG';

  @override
  String get content => '内容';

  @override
  String get trafficGuide => '引流指南';

  @override
  String get create => '创建';

  @override
  String get edit => '编辑';

  @override
  String get delete => '删除';

  @override
  String get save => '保存';

  @override
  String get cancel => '取消';

  @override
  String get confirm => '确认';

  @override
  String get yes => '是';

  @override
  String get no => '否';

  @override
  String get ok => '确定';

  @override
  String get loading => '加载中...';

  @override
  String get error => '错误';

  @override
  String get success => '成功';

  @override
  String get warning => '警告';

  @override
  String get info => '信息';

  @override
  String get search => '搜索';

  @override
  String get searchHint => '请输入搜索内容...';

  @override
  String get noResults => '未找到结果';

  @override
  String get tryAgain => '重试';

  @override
  String get refresh => '刷新';

  @override
  String get share => '分享';

  @override
  String get export => '导出';

  @override
  String get import => '导入';

  @override
  String get copy => '复制';

  @override
  String get paste => '粘贴';

  @override
  String get cut => '剪切';

  @override
  String get undo => '撤销';

  @override
  String get redo => '重做';

  @override
  String get selectAll => '全选';

  @override
  String get close => '关闭';

  @override
  String get back => '返回';

  @override
  String get next => '下一步';

  @override
  String get previous => '上一步';

  @override
  String get done => '完成';

  @override
  String get finish => '结束';

  @override
  String get skip => '跳过';

  @override
  String get continueAction => '继续';

  @override
  String get retry => '重试';

  @override
  String get reset => '重置';

  @override
  String get clear => '清除';

  @override
  String get apply => '应用';

  @override
  String get preview => '预览';

  @override
  String get download => '下载';

  @override
  String get upload => '上传';

  @override
  String get file => '文件';

  @override
  String get folder => '文件夹';

  @override
  String get name => '名称';

  @override
  String get title => '标题';

  @override
  String get description => '描述';

  @override
  String get size => '大小';

  @override
  String get date => '日期';

  @override
  String get time => '时间';

  @override
  String get type => '类型';

  @override
  String get status => '状态';

  @override
  String get version => '版本';

  @override
  String get author => '作者';

  @override
  String get tags => '标签';

  @override
  String get category => '分类';

  @override
  String get priority => '优先级';

  @override
  String get high => '高';

  @override
  String get medium => '中';

  @override
  String get low => '低';

  @override
  String get enabled => '已启用';

  @override
  String get disabled => '已禁用';

  @override
  String get online => '在线';

  @override
  String get offline => '离线';

  @override
  String get connected => '已连接';

  @override
  String get disconnected => '已断开';

  @override
  String get available => '可用';

  @override
  String get unavailable => '不可用';

  @override
  String get active => '活跃';

  @override
  String get inactive => '非活跃';

  @override
  String get public => '公开';

  @override
  String get private => '私有';

  @override
  String get draft => '草稿';

  @override
  String get published => '已发布';

  @override
  String get archived => '已归档';

  @override
  String get pdfProfessionalTool => 'PDF 专业工具';

  @override
  String get pdfToolDescription => '强大的PDF处理能力，让文档管理更简单';

  @override
  String get securityEncryption => '安全加密';

  @override
  String get passwordProtectionPermissionControl => '密码保护\n权限控制';

  @override
  String get intelligentAnnotation => '智能注释';

  @override
  String get highlightMarkingTextAnnotation => '高亮标记\n文字批注';

  @override
  String get quickSearch => '快速搜索';

  @override
  String get fullTextSearchContentLocation => '全文检索\n内容定位';

  @override
  String get convenientSharing => '便捷分享';

  @override
  String get multipleFormatsOneClickExport => '多种格式\n一键导出';

  @override
  String get welcomeToPdfTool => '欢迎使用PDF专业工具！';

  @override
  String get importFirstPdfDocument => '导入第一个PDF文档';

  @override
  String get appearance => '外观';

  @override
  String get followSystem => '跟随系统';

  @override
  String get languageChangeEffect => '更改语言后，应用将立即生效';

  @override
  String get contentLibrary => '内容库';

  @override
  String get manageAllCards => '管理所有卡片';

  @override
  String get templateLibrary => '模板库';

  @override
  String get browseBeautifulTemplates => '浏览精美模板';

  @override
  String get inputTextToSplit => '输入要拆分的文本';

  @override
  String get pasteOrInputLongText => '粘贴或输入长文本内容，系统将智能识别并拆分为多个卡片';

  @override
  String get pasteClipboard => '粘贴剪贴板';

  @override
  String get clearContent => '清空内容';

  @override
  String cardNumber(int number) {
    return '卡片 $number';
  }

  @override
  String get loadingDemoData => '正在加载演示数据...';

  @override
  String get modernUIDesign => '✨ 现代化UI设计\n🖼️ 渲染结果预览\n📱 分块模式支持\n⚡ 高性能体验';

  @override
  String editFunction(String title) {
    return '编辑功能：$title';
  }

  @override
  String deleted(String title) {
    return '已删除：$title';
  }

  @override
  String shareFunction(String title) {
    return '分享功能：$title';
  }

  @override
  String get createNewContent => '创建新内容';

  @override
  String get selectContentType => '选择您要创建的内容类型';

  @override
  String get bold => '**粗体**';

  @override
  String get italic => '*斜体*';

  @override
  String get heading1 => '# 标题1';

  @override
  String get heading2 => '## 标题2';

  @override
  String get heading3 => '### 标题3';

  @override
  String get list => '- 列表项\n- 列表项';

  @override
  String get link => '[链接文本](URL)';

  @override
  String get image => '![图片描述](图片URL)';

  @override
  String get code => '`代码`';

  @override
  String get codeBlock => '```\n代码块\n```';

  @override
  String get quote => '> 引用文本';

  @override
  String get table => '| 列1 | 列2 |\n| --- | --- |\n| 内容1 | 内容2 |';

  @override
  String get myContentLibrary => '我的内容库';

  @override
  String get manageAndBrowseContent => '管理和浏览您的所有内容';

  @override
  String get contentTools => '内容工具';

  @override
  String get recommendedTools => '推荐工具';

  @override
  String get markdownTitle => 'Markdown';

  @override
  String get markdownDescription => '文档编辑与渲染';

  @override
  String get textCardsTitle => '文本卡片';

  @override
  String get textCardsDescription => '知识卡片定制渲染';

  @override
  String get trafficGuideTitle => '内容引流';

  @override
  String get trafficGuideDescription => '引流图片与文本处理';

  @override
  String get fileTools => '文件工具';

  @override
  String get svgTitle => 'SVG';

  @override
  String get svgDescription => '矢量图形处理';

  @override
  String get htmlTitle => 'HTML';

  @override
  String get htmlDescription => '网页内容编辑';

  @override
  String get loadingContent => '正在加载内容...';

  @override
  String languageChangedTo(String language) {
    return '语言已更改为 $language';
  }

  @override
  String get developer => '开发者';

  @override
  String get contentLibraryDemo => '内容库演示';

  @override
  String get viewNewContentLibraryFeatures => '查看新的内容库功能';

  @override
  String get i18nDemo => '国际化演示';

  @override
  String get viewMultiLanguageSupport => '查看多语言支持效果';

  @override
  String get about => '关于';

  @override
  String get versionInfo => '版本信息';

  @override
  String get helpAndFeedback => '帮助与反馈';

  @override
  String get getHelpOrProvideFeedback => '获取帮助或提供反馈';

  @override
  String get helpAndFeedbackContent =>
      '如果您有任何问题或建议，请通过以下方式联系我们：\n\n邮箱：<EMAIL>';

  @override
  String get selectTheme => '选择主题';

  @override
  String get lightMode => '浅色模式';

  @override
  String get darkMode => '深色模式';

  @override
  String get systemMode => '跟随系统';

  @override
  String get personalizeYourAppExperience => '个性化您的应用体验';
}
