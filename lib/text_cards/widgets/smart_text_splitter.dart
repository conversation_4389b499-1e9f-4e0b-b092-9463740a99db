import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../models/split_config.dart';
import '../models/enhanced_card_template.dart';
import '../services/smart_split_service.dart';
import 'split_preview_card.dart';
import '../../generated/l10n/app_localizations.dart';

/// 智能文本拆分器
class SmartTextSplitter extends StatefulWidget {
  final Function(List<String> contents, EnhancedCardTemplate template) onBatchCreate;

  const SmartTextSplitter({
    super.key,
    required this.onBatchCreate,
  });

  @override
  State<SmartTextSplitter> createState() => _SmartTextSplitterState();
}

class _SmartTextSplitterState extends State<SmartTextSplitter>
    with TickerProviderStateMixin {
  late AnimationController _slideController;
  late Animation<Offset> _slideAnimation;
  late AnimationController _fadeController;
  late Animation<double> _fadeAnimation;

  final TextEditingController _textController = TextEditingController();
  final TextEditingController _separatorController = TextEditingController();
  final PageController _pageController = PageController();

  SplitConfig _config = const SplitConfig();
  List<SplitResultItem> _splitResults = [];
  Set<int> _selectedIndices = {};
  int _currentPage = 0; // 0: 输入, 1: 配置, 2: 预览
  bool _isProcessing = false;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _initializeDefaultText();
  }

  void _initializeAnimations() {
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOutCubic,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));

    _slideController.forward();
    _fadeController.forward();
  }

  void _initializeDefaultText() {
    _textController.text = '''什么是人工智能？

人工智能（Artificial Intelligence，AI）是计算机科学的一个分支，它企图了解智能的实质，并生产出一种新的能以人类智能相似的方式做出反应的智能机器。

---

机器学习的基本概念

机器学习是人工智能的一个重要分支，它使计算机能够在没有明确编程的情况下学习和改进。主要包括：

• 监督学习：使用标记数据训练模型
• 无监督学习：从未标记数据中发现模式
• 强化学习：通过试错来学习最优策略

---

深度学习的应用

深度学习是机器学习的一个子集，它模仿人脑神经网络的工作方式。在以下领域有广泛应用：

1. 图像识别和计算机视觉
2. 自然语言处理
3. 语音识别和合成
4. 推荐系统''';

    _separatorController.text = '---';
  }

  @override
  void dispose() {
    _slideController.dispose();
    _fadeController.dispose();
    _textController.dispose();
    _separatorController.dispose();
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SlideTransition(
      position: _slideAnimation,
      child: Container(
        height: MediaQuery.of(context).size.height * 0.9,
        decoration: const BoxDecoration(
          color: Color(0xFFF8FAFC),
          borderRadius: BorderRadius.vertical(top: Radius.circular(25)),
        ),
        child: Column(
          children: [
            _buildHeader(),
            _buildProgressIndicator(),
            Expanded(
              child: FadeTransition(
                opacity: _fadeAnimation,
                child: PageView(
                  controller: _pageController,
                  onPageChanged: (page) {
                    setState(() {
                      _currentPage = page;
                    });
                  },
                  children: [
                    _buildInputPage(),
                    _buildConfigPage(),
                    _buildPreviewPage(),
                  ],
                ),
              ),
            ),
            _buildBottomActions(),
          ],
        ),
      ),
    );
  }

  /// 构建头部
  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(25)),
        boxShadow: [
          BoxShadow(
            color: Color(0x0A000000),
            offset: Offset(0, 1),
            blurRadius: 3,
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              gradient: const LinearGradient(
                colors: [Color(0xFF8B5CF6), Color(0xFF6366F1)],
              ),
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Icon(
              Icons.auto_awesome,
              color: Colors.white,
              size: 20,
            ),
          ),
          const SizedBox(width: 16),
          const Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '智能文本拆分',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w700,
                    color: Color(0xFF1E293B),
                  ),
                ),
                Text(
                  '将长文本智能分割为多个卡片',
                  style: TextStyle(
                    fontSize: 12,
                    color: Color(0xFF64748B),
                  ),
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: () => Navigator.pop(context),
            icon: const Icon(
              Icons.close,
              color: Color(0xFF64748B),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建进度指示器
  Widget _buildProgressIndicator() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
      color: Colors.white,
      child: Row(
        children: [
          _buildStepIndicator(0, '输入文本', Icons.edit_outlined),
          _buildStepConnector(),
          _buildStepIndicator(1, '配置拆分', Icons.settings_outlined),
          _buildStepConnector(),
          _buildStepIndicator(2, '预览结果', Icons.preview_outlined),
        ],
      ),
    );
  }

  /// 构建步骤指示器
  Widget _buildStepIndicator(int step, String title, IconData icon) {
    final isActive = _currentPage == step;
    final isCompleted = _currentPage > step;

    return Expanded(
      child: Column(
        children: [
          Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              color: isActive || isCompleted
                ? const Color(0xFF6366F1)
                : const Color(0xFFE2E8F0),
              borderRadius: BorderRadius.circular(16),
            ),
            child: Icon(
              isCompleted ? Icons.check : icon,
              size: 16,
              color: isActive || isCompleted ? Colors.white : const Color(0xFF64748B),
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: TextStyle(
              fontSize: 10,
              fontWeight: FontWeight.w500,
              color: isActive ? const Color(0xFF6366F1) : const Color(0xFF64748B),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建步骤连接器
  Widget _buildStepConnector() {
    return Container(
      width: 24,
      height: 2,
      color: const Color(0xFFE2E8F0),
      margin: const EdgeInsets.only(bottom: 16),
    );
  }

  /// 构建输入页面
  Widget _buildInputPage() {
    final l10n = AppLocalizations.of(context);
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            l10n.inputTextToSplit,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Color(0xFF1E293B),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            l10n.pasteOrInputLongText,
            style: const TextStyle(
              fontSize: 12,
              color: Color(0xFF64748B),
            ),
          ),
          const SizedBox(height: 16),

          // 快速操作按钮
          Row(
            children: [
              _buildQuickActionButton(
                l10n.pasteClipboard,
                Icons.content_paste,
                _pasteFromClipboard,
              ),
              const SizedBox(width: 12),
              _buildQuickActionButton(
                l10n.clearContent,
                Icons.clear_all,
                _clearText,
              ),
            ],
          ),

          const SizedBox(height: 16),

          // 文本输入框
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: const Color(0xFFE2E8F0)),
              ),
              child: TextField(
                controller: _textController,
                decoration: const InputDecoration(
                  hintText: '在这里输入或粘贴文本内容...',
                  border: InputBorder.none,
                  contentPadding: EdgeInsets.all(16),
                ),
                maxLines: null,
                expands: true,
                textAlignVertical: TextAlignVertical.top,
                style: const TextStyle(
                  fontSize: 14,
                  height: 1.5,
                ),
              ),
            ),
          ),

          const SizedBox(height: 16),

          // 统计信息
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: const Color(0xFF6366F1).withValues(alpha: 0.05),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                const Icon(
                  Icons.info_outline,
                  size: 16,
                  color: Color(0xFF6366F1),
                ),
                const SizedBox(width: 8),
                Text(
                  '字符数：${_textController.text.length}',
                  style: const TextStyle(
                    fontSize: 12,
                    color: Color(0xFF6366F1),
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建配置页面
  Widget _buildConfigPage() {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '拆分配置',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Color(0xFF1E293B),
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            '选择拆分模式和相关参数',
            style: TextStyle(
              fontSize: 12,
              color: Color(0xFF64748B),
            ),
          ),
          const SizedBox(height: 20),

          Expanded(
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 拆分模式选择
                  _buildSectionTitle('拆分模式'),
                  const SizedBox(height: 12),
                  ...SplitMode.values.map((mode) => _buildModeOption(mode)),

                  const SizedBox(height: 24),

                  // 自定义分隔符（仅在自定义模式下显示）
                  if (_config.mode == SplitMode.custom) ...[
                    _buildSectionTitle('自定义分隔符'),
                    const SizedBox(height: 12),
                    Container(
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: const Color(0xFFE2E8F0)),
                      ),
                      child: TextField(
                        controller: _separatorController,
                        decoration: const InputDecoration(
                          hintText: '输入分隔符，如：---',
                          border: InputBorder.none,
                          contentPadding: EdgeInsets.all(12),
                        ),
                        onChanged: (value) {
                          setState(() {
                            _config = _config.copyWith(customSeparator: value);
                          });
                        },
                      ),
                    ),
                    const SizedBox(height: 24),
                  ],

                  // 高级选项
                  _buildSectionTitle('高级选项'),
                  const SizedBox(height: 12),
                  _buildAdvancedOptions(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
  /// 构建预览页面
  Widget _buildPreviewPage() {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Text(
                '拆分预览',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Color(0xFF1E293B),
                ),
              ),
              const Spacer(),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: const Color(0xFF10B981).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Text(
                  '共 ${_splitResults.length} 个卡片',
                  style: const TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    color: Color(0xFF10B981),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          const Text(
            '预览拆分结果，可以编辑、合并或删除卡片',
            style: TextStyle(
              fontSize: 12,
              color: Color(0xFF64748B),
            ),
          ),
          const SizedBox(height: 16),

          // 批量操作按钮
          Row(
            children: [
              _buildBatchActionButton(
                '全选',
                Icons.select_all,
                _selectAll,
              ),
              const SizedBox(width: 8),
              _buildBatchActionButton(
                '取消选择',
                Icons.deselect,
                _deselectAll,
              ),
              const SizedBox(width: 8),
              _buildBatchActionButton(
                '删除选中',
                Icons.delete_outline,
                _deleteSelected,
                color: const Color(0xFFEF4444),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // 拆分结果列表
          Expanded(
            child: _splitResults.isEmpty
                ? _buildEmptyState()
                : ListView.builder(
                    itemCount: _splitResults.length,
                    itemBuilder: (context, index) {
                      final item = _splitResults[index];
                      return SplitPreviewCard(
                        item: item,
                        index: index,
                        isSelected: _selectedIndices.contains(index),
                        onTap: () => _toggleSelection(index),
                        onEdit: () => _editItem(index),
                        onDelete: () => _deleteItem(index),
                        onMerge: index < _splitResults.length - 1
                            ? () => _mergeWithNext(index)
                            : null,
                      );
                    },
                  ),
          ),
        ],
      ),
    );
  }

  /// 构建空状态
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.content_cut,
            size: 48,
            color: Colors.grey.withValues(alpha: 0.4),
          ),
          const SizedBox(height: 16),
          const Text(
            '没有拆分结果',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: Color(0xFF64748B),
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            '请返回上一步检查输入文本和配置',
            style: TextStyle(
              fontSize: 12,
              color: Color(0xFF94A3B8),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建底部操作按钮
  Widget _buildBottomActions() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: const BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Color(0x0A000000),
            offset: Offset(0, -1),
            blurRadius: 3,
          ),
        ],
      ),
      child: Row(
        children: [
          if (_currentPage > 0)
            Expanded(
              child: OutlinedButton(
                onPressed: _previousPage,
                style: OutlinedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  side: const BorderSide(color: Color(0xFFE2E8F0)),
                ),
                child: const Text(
                  '上一步',
                  style: TextStyle(
                    fontWeight: FontWeight.w600,
                    color: Color(0xFF64748B),
                  ),
                ),
              ),
            ),

          if (_currentPage > 0) const SizedBox(width: 12),

          Expanded(
            flex: _currentPage == 0 ? 1 : 2,
            child: ElevatedButton(
              onPressed: _getNextButtonAction(),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF6366F1),
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                elevation: 0,
              ),
              child: _isProcessing
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : Text(
                      _getNextButtonText(),
                      style: const TextStyle(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
            ),
          ),
        ],
      ),
    );
  }
  // ===== 辅助方法 =====

  /// 构建快速操作按钮
  Widget _buildQuickActionButton(String text, IconData icon, VoidCallback onTap) {
    return Expanded(
      child: Container(
        height: 40,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: const Color(0xFFE2E8F0)),
        ),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            onTap: onTap,
            borderRadius: BorderRadius.circular(8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(icon, size: 16, color: const Color(0xFF6366F1)),
                const SizedBox(width: 8),
                Text(
                  text,
                  style: const TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    color: Color(0xFF6366F1),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 构建节标题
  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: const TextStyle(
        fontSize: 14,
        fontWeight: FontWeight.w600,
        color: Color(0xFF1E293B),
      ),
    );
  }

  /// 构建模式选项
  Widget _buildModeOption(SplitMode mode) {
    final isSelected = _config.mode == mode;

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: isSelected ? const Color(0xFF6366F1) : const Color(0xFFE2E8F0),
          width: isSelected ? 2 : 1,
        ),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () {
            setState(() {
              _config = _config.copyWith(mode: mode);
            });
          },
          borderRadius: BorderRadius.circular(8),
          child: Padding(
            padding: const EdgeInsets.all(12),
            child: Row(
              children: [
                Container(
                  width: 20,
                  height: 20,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: isSelected ? const Color(0xFF6366F1) : const Color(0xFFD1D5DB),
                      width: 2,
                    ),
                    color: isSelected ? const Color(0xFF6366F1) : Colors.transparent,
                  ),
                  child: isSelected
                      ? const Icon(Icons.check, size: 12, color: Colors.white)
                      : null,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        mode.displayName,
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: isSelected ? const Color(0xFF6366F1) : const Color(0xFF1E293B),
                        ),
                      ),
                      const SizedBox(height: 2),
                      Text(
                        mode.description,
                        style: const TextStyle(
                          fontSize: 12,
                          color: Color(0xFF64748B),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 构建高级选项
  Widget _buildAdvancedOptions() {
    return Column(
      children: [
        _buildSwitchOption(
          '自动识别标题',
          '智能识别文本中的标题内容',
          _config.autoDetectTitles,
          (value) {
            setState(() {
              _config = _config.copyWith(autoDetectTitles: value);
            });
          },
        ),
        const SizedBox(height: 12),
        _buildSwitchOption(
          '保留格式',
          '保留原文本的Markdown格式',
          _config.preserveFormatting,
          (value) {
            setState(() {
              _config = _config.copyWith(preserveFormatting: value);
            });
          },
        ),
        const SizedBox(height: 12),
        _buildSwitchOption(
          '智能合并',
          '自动合并过短的段落',
          _config.smartMerge,
          (value) {
            setState(() {
              _config = _config.copyWith(smartMerge: value);
            });
          },
        ),
        const SizedBox(height: 16),
        _buildSliderOption(
          '最大长度',
          '每个卡片的最大字符数',
          _config.maxLength.toDouble(),
          100,
          1000,
          (value) {
            setState(() {
              _config = _config.copyWith(maxLength: value.round());
            });
          },
        ),
      ],
    );
  }

  /// 构建开关选项
  Widget _buildSwitchOption(
    String title,
    String description,
    bool value,
    ValueChanged<bool> onChanged,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: const Color(0xFFE2E8F0)),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: Color(0xFF1E293B),
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  description,
                  style: const TextStyle(
                    fontSize: 12,
                    color: Color(0xFF64748B),
                  ),
                ),
              ],
            ),
          ),
          Switch(
            value: value,
            onChanged: onChanged,
            activeColor: const Color(0xFF6366F1),
          ),
        ],
      ),
    );
  }
  /// 构建滑块选项
  Widget _buildSliderOption(
    String title,
    String description,
    double value,
    double min,
    double max,
    ValueChanged<double> onChanged,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: const Color(0xFFE2E8F0)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: Color(0xFF1E293B),
                      ),
                    ),
                    const SizedBox(height: 2),
                    Text(
                      description,
                      style: const TextStyle(
                        fontSize: 12,
                        color: Color(0xFF64748B),
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: const Color(0xFF6366F1).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Text(
                  value.round().toString(),
                  style: const TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: Color(0xFF6366F1),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          SliderTheme(
            data: SliderTheme.of(context).copyWith(
              activeTrackColor: const Color(0xFF6366F1),
              inactiveTrackColor: const Color(0xFFE2E8F0),
              thumbColor: const Color(0xFF6366F1),
              overlayColor: const Color(0xFF6366F1).withValues(alpha: 0.1),
              trackHeight: 4,
            ),
            child: Slider(
              value: value,
              min: min,
              max: max,
              divisions: ((max - min) / 50).round(),
              onChanged: onChanged,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建批量操作按钮
  Widget _buildBatchActionButton(
    String text,
    IconData icon,
    VoidCallback onTap, {
    Color color = const Color(0xFF6366F1),
  }) {
    return Container(
      height: 32,
      padding: const EdgeInsets.symmetric(horizontal: 12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(6),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(6),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(icon, size: 14, color: color),
              const SizedBox(width: 6),
              Text(
                text,
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                  color: color,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // ===== 事件处理方法 =====

  /// 从剪贴板粘贴
  Future<void> _pasteFromClipboard() async {
    try {
      final data = await Clipboard.getData(Clipboard.kTextPlain);
      if (data?.text != null) {
        setState(() {
          _textController.text = data!.text!;
        });
      }
    } catch (e) {
      // 忽略错误
    }
  }

  /// 清空文本
  void _clearText() {
    setState(() {
      _textController.clear();
    });
  }

  /// 上一页
  void _previousPage() {
    if (_currentPage > 0) {
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  /// 下一页或执行操作
  VoidCallback? _getNextButtonAction() {
    if (_isProcessing) return null;

    switch (_currentPage) {
      case 0:
        return _textController.text.trim().isNotEmpty ? _nextPage : null;
      case 1:
        return _performSplit;
      case 2:
        return _splitResults.isNotEmpty ? _createCards : null;
    }
    return null;
  }

  /// 获取下一步按钮文本
  String _getNextButtonText() {
    switch (_currentPage) {
      case 0:
        return '下一步';
      case 1:
        return '开始拆分';
      case 2:
        return '创建卡片';
      default:
        return '下一步';
    }
  }

  /// 下一页
  void _nextPage() {
    if (_currentPage < 2) {
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  /// 执行拆分
  Future<void> _performSplit() async {
    setState(() {
      _isProcessing = true;
    });

    try {
      await Future.delayed(const Duration(milliseconds: 500)); // 模拟处理时间

      final results = SmartSplitService.splitText(_textController.text, _config);

      setState(() {
        _splitResults = results;
        _selectedIndices.clear();
        _isProcessing = false;
      });

      _nextPage();
    } catch (e) {
      setState(() {
        _isProcessing = false;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('拆分失败：$e')),
      );
    }
  }
  /// 创建卡片
  Future<void> _createCards() async {
    if (_splitResults.isEmpty) return;

    setState(() {
      _isProcessing = true;
    });

    try {
      // 提取所有内容
      final contents = _splitResults.map((item) => item.content).toList();

      // 使用默认模板
      final template = EnhancedCardTemplate.getModernTemplates().first;

      // 调用回调函数
      widget.onBatchCreate(contents, template);

      // 关闭弹窗
      Navigator.pop(context);
    } catch (e) {
      setState(() {
        _isProcessing = false;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('创建卡片失败：$e')),
      );
    }
  }

  /// 切换选择状态
  void _toggleSelection(int index) {
    setState(() {
      if (_selectedIndices.contains(index)) {
        _selectedIndices.remove(index);
      } else {
        _selectedIndices.add(index);
      }
    });
  }

  /// 全选
  void _selectAll() {
    setState(() {
      _selectedIndices = Set.from(List.generate(_splitResults.length, (i) => i));
    });
  }

  /// 取消选择
  void _deselectAll() {
    setState(() {
      _selectedIndices.clear();
    });
  }

  /// 删除选中项
  void _deleteSelected() {
    if (_selectedIndices.isEmpty) return;

    setState(() {
      final sortedIndices = _selectedIndices.toList()..sort((a, b) => b.compareTo(a));
      for (final index in sortedIndices) {
        _splitResults.removeAt(index);
      }
      _selectedIndices.clear();
    });
  }

  /// 编辑项
  void _editItem(int index) {
    if (index >= _splitResults.length) return;

    final item = _splitResults[index];
    final titleController = TextEditingController(text: item.title ?? '');
    final contentController = TextEditingController(text: item.content);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('编辑卡片'),
        content: SizedBox(
          width: double.maxFinite,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: titleController,
                decoration: const InputDecoration(
                  labelText: '标题（可选）',
                  border: OutlineInputBorder(),
                ),
              ),
              const SizedBox(height: 16),
              TextField(
                controller: contentController,
                decoration: const InputDecoration(
                  labelText: '内容',
                  border: OutlineInputBorder(),
                ),
                maxLines: 5,
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () {
              setState(() {
                _splitResults[index] = item.copyWith(
                  title: titleController.text.trim().isEmpty ? null : titleController.text.trim(),
                  content: contentController.text,
                );
              });
              Navigator.pop(context);
            },
            child: const Text('保存'),
          ),
        ],
      ),
    );
  }

  /// 删除项
  void _deleteItem(int index) {
    if (index >= _splitResults.length) return;

    setState(() {
      _splitResults.removeAt(index);
      _selectedIndices.remove(index);

      // 更新选中索引
      final newSelectedIndices = <int>{};
      for (final selectedIndex in _selectedIndices) {
        if (selectedIndex > index) {
          newSelectedIndices.add(selectedIndex - 1);
        } else {
          newSelectedIndices.add(selectedIndex);
        }
      }
      _selectedIndices = newSelectedIndices;
    });
  }

  /// 与下一项合并
  void _mergeWithNext(int index) {
    if (index >= _splitResults.length - 1) return;

    setState(() {
      _splitResults = SmartSplitService.mergeItems(_splitResults, index, index + 1);
      _selectedIndices.clear();
    });
  }
}
