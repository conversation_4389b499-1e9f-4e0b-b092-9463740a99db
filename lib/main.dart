import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:path_provider/path_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'common/utils/permission_helper.dart';
import 'config/app_theme.dart';
import 'config/constants.dart';
import 'generated/l10n/app_localizations.dart';
import 'home.dart';
import 'services/service_locator.dart';
import 'services/file_intent_service.dart';
import 'services/localization_service.dart';


// 方法通道用于处理文件意图
const platform = MethodChannel('com.example.contentpal/file_intent');

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  PermissionHelper.checkIosPermissions();

  // 设置状态栏
  SystemChrome.setSystemUIOverlayStyle(
    const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarIconBrightness: Brightness.dark,
      statusBarBrightness: Brightness.light,
    ),
  );

  // 设置设备方向
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  // 初始化数据存储
  await initializeStorage();

  // 初始化服务定位器
  await ServiceLocator().initServices();

  // 启动应用
  runApp(const ContentPalApp());
}

// 初始化本地存储
Future<void> initializeStorage() async {
  final appDocumentDir = await getApplicationDocumentsDirectory();
  await Hive.initFlutter(appDocumentDir.path);
  await Hive.openBox(AppConstants.mainBoxName);

  // 检查首次启动
  final prefs = await SharedPreferences.getInstance();
  final hasLaunchedBefore = prefs.getBool(AppConstants.keyHasLaunchedBefore);

  if (hasLaunchedBefore == null) {
    // 首次启动，初始化默认设置
    await prefs.setBool(AppConstants.keyHasLaunchedBefore, true);
    // 其他首次启动设置...
  }
}

class ContentPalApp extends StatefulWidget {
  const ContentPalApp({super.key});

  @override
  State<ContentPalApp> createState() => _ContentPalAppState();
}

class _ContentPalAppState extends State<ContentPalApp>
    with WidgetsBindingObserver {
  ThemeMode _themeMode = ThemeMode.system;
  final FileIntentService _fileIntentService = FileIntentService();
  final LocalizationService _localizationService = LocalizationService();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _loadThemeMode();
    _setupFileIntentHandler();
    _setupThemeCallback();
    _initializeLocalization();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangePlatformBrightness() {
    super.didChangePlatformBrightness();
    // If user has selected "Follow System", update theme when system theme changes
    if (_themeMode == ThemeMode.system) {
      setState(() {
        // This will trigger a rebuild with the new system theme
      });
    }
  }

  /// 初始化本地化服务
  Future<void> _initializeLocalization() async {
    await _localizationService.initialize();
  }

  // 设置文件意图处理
  void _setupFileIntentHandler() {
    // 设置方法通道监听
    platform.setMethodCallHandler(_handleMethodCall);

    // 检查应用启动时是否有文件意图
    _checkInitialFileIntent();
  }

  // 处理方法通道调用
  Future<void> _handleMethodCall(MethodCall call) async {
    switch (call.method) {
      case 'handleFileIntent':
        final String? filePath = call.arguments['filePath'] as String?;
        if (filePath != null) {
          _handleFileIntent(filePath);
        }
        break;
      case 'handleTextIntent':
        final String? text = call.arguments['text'] as String?;
        if (text != null) {
          _handleTextIntent(text);
        }
        break;
    }
  }

  // 检查初始文件意图
  Future<void> _checkInitialFileIntent() async {
    try {
      final result = await platform.invokeMethod('getInitialIntent');
      if (result != null) {
        final Map<String, dynamic> intentData = Map<String, dynamic>.from(
          result,
        );
        final String? type = intentData['type'] as String?;

        if (type == 'file') {
          final String? filePath = intentData['filePath'] as String?;
          if (filePath != null) {
            _handleFileIntent(filePath);
          }
        } else if (type == 'text') {
          final String? text = intentData['text'] as String?;
          if (text != null) {
            _handleTextIntent(text);
          }
        }
      }
    } catch (e) {
      debugPrint('获取初始意图失败: $e');
    }
  }

  // 处理文件意图
  void _handleFileIntent(String filePath) {
    final context = ServiceLocator().globalKey.currentContext;
    if (context != null) {
      _fileIntentService.handleFileOpen(context, filePath);
    }
  }

  // 处理文本意图
  void _handleTextIntent(String text) {
    final context = ServiceLocator().globalKey.currentContext;
    if (context != null) {
      _fileIntentService.handleTextContent(context, text, title: '分享的文本');
    }
  }

  // 设置主题变更回调
  void _setupThemeCallback() {
    ServiceLocator().settingsService.setThemeChangeCallback(_updateThemeMode);
  }

  // 加载主题设置
  Future<void> _loadThemeMode() async {
    try {
      // 等待服务初始化完成
      if (ServiceLocator().isInitialized) {
        final settings = ServiceLocator().settingsService.settings;
        setState(() {
          _themeMode = settings.themeMode;
        });
      } else {
        // 如果服务还未初始化，延迟加载
        Future.delayed(const Duration(milliseconds: 100), () {
          if (mounted && ServiceLocator().isInitialized) {
            final settings = ServiceLocator().settingsService.settings;
            setState(() {
              _themeMode = settings.themeMode;
            });
          }
        });
      }
    } catch (e) {
      debugPrint('加载主题设置失败: $e');
      // 使用默认主题
      setState(() {
        _themeMode = ThemeMode.system;
      });
    }
  }

  // 更新主题模式
  void _updateThemeMode(ThemeMode themeMode) {
    setState(() {
      _themeMode = themeMode;
    });
  }

  @override
  Widget build(BuildContext context) {
    return ListenableBuilder(
      listenable: _localizationService,
      builder: (context, child) {
        // Get user's theme type preference
        final settings =
            ServiceLocator().isInitialized
                ? ServiceLocator().settingsService.settings
                : null;
        final themeType = settings?.themeType ?? AppThemeType.materialYou;

        return MaterialApp(
          navigatorKey: ServiceLocator().globalKey,
          title: AppConstants.appName,
          debugShowCheckedModeBanner: false,
          theme: AppTheme.getTheme(themeType, false),
          darkTheme: AppTheme.getTheme(themeType, true),
          themeMode: _themeMode,
          // 国际化配置
          localizationsDelegates: const [
            AppLocalizations.delegate,
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          supportedLocales: LocalizationService.supportedLocales,
          locale: _localizationService.currentLocale,
          home: HomePage(localizationService: _localizationService),
          routes: {
            '/home':
                (context) =>
                    HomePage(localizationService: _localizationService),
          },
        );
      },
    );
  }


}
